import { FormRules } from '@wuk/wui'

export const useGroupRules = () => {
  const groupRules: FormRules = {
    group_name: [{ required: true, message: 'Please input group name', trigger: 'change' }]
  }
  return {
    groupRules
  }
}
export const useEquationRules = () => {
  const equationRules: FormRules = {
    name: [{ required: true, message: 'Please input equation name', trigger: 'change' }],
    equation: [{ required: true, message: 'Please input equation', trigger: 'change' }]
  }
  return {
    equationRules
  }
}
