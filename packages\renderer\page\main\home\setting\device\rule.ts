import { ref } from 'vue'
import { FormRules } from '@wuk/wui'
import {
  validStrNumIntOrNumInt,
  validRange,
  validApi,
  setRuleErrorCallback
} from '@/renderer/utils/rules'
export const useDeviceRules = () => {
  const deviceErrors = ref<Record<string, string>>({})
  const deviceRules: FormRules = {
    name: { required: true, message: 'Please input device name', trigger: 'change' },
    scan_rate: [
      { required: true, message: 'Please input scan rate', trigger: 'change' },
      { trigger: 'change', validator: validRange(0, 200) }
    ]
  }
  const setDeviceError = (key: string, value = '') => {
    setRuleErrorCallback(deviceErrors, key, value)
  }
  return {
    deviceRules,
    deviceErrors,
    setDeviceError
  }
}
