import { useBem, usePermanentFocus, useTableCommonMenu, useBizEngine } from '@/renderer/hooks'
import { defineComponent, ref, computed, watch, useModel, onMounted } from 'vue'
import $styles from './index.module.scss'
import TableTool, { isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import { SetupLimitTableRow, LimitTableRow, currentParameters } from '../type'
export default defineComponent({
  name: 'LimitTable',
  props: {
    limitType: {
      type: String as () => 'start' | 'run',
      required: true
    },
    currentParameter: {
      type: Object as () => currentParameters,
      default: () => {}
    }
  },
  emits: ['limit-actions', 'add-table-row'],
  setup(props, { expose, emit }) {
    const { e } = useBem('setup-limit', $styles)
    const currentParameter = useModel(props, 'currentParameter')
    const { handleInputClick, cancelPermanentFocus } = usePermanentFocus<SetupLimitTableRow>()
    const currentTableItem = ref()

    const data = ref<SetupLimitTableRow[]>([])

    const handleSelect = (value: string) => {
      currentTableItem.value.limit = value
    }

    const getDataInfo = () => {
      const limitConfig =
        props.limitType === 'start'
          ? currentParameter.value.parameter?.start
          : currentParameter.value.parameter?.run

      limitConfig?.cfg_list.forEach(cfg => {
        data.value.push({
          ...cfg,
          flag: false,
          row_type: '*'
        })
      })
    }
    const columns: Array<{
      prop: keyof SetupLimitTableRow
      label: string
      align?: 'center'
      width?: string
      isPermanentFocus?: boolean
    }> = [
      {
        prop: 'limit',
        label: 'Limit',
        align: 'center',
        isPermanentFocus: true
      },
      {
        prop: 'deadband',
        label: 'Deadband',
        align: 'center',
        width: '120'
      },
      {
        prop: 'wait_sec',
        label: 'Wait(sec)',
        align: 'center',
        width: '120'
      }
    ]
    const createRow = (row_type: SetupLimitTableRow['row_type']) => ({
      limit: '$LOWERS',
      deadband: 0.0,
      wait_sec: 0.0,
      below_message: '',
      above_message: '',
      phase: '',
      color: '',
      alarm: '',
      normality: 0,
      store_event: false,
      flag: true,
      row_type
    })

    const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
      data,
      (key, ...args) => {
        const { row, rowIndex } = args[0]
        switch (key) {
          case 'addKey':
            const newRow = createRow('add')
            data.value.push(newRow)
            currentTableItem.value = newRow
            emit('add-table-row')
            break
          case 'deleteKey':
            handleOp('delete', row, rowIndex)
            break
          case 'modifyKey':
            handleOp('edit', row, rowIndex)
            currentTableItem.value = row
            break
          case 'insertKey':
            const insertRow = createRow('insert')
            data.value.splice(rowIndex + 1, 0, insertRow)
            currentTableItem.value = insertRow
            emit('add-table-row')
            break
          case 'limitActionsKey':
            emit('limit-actions', row, 'table')
        }
      },
      [1],
      [
        { key: 'addKey', label: 'add' },
        { key: 'insertKey', label: 'insert' },
        { key: 'modifyKey', label: 'modify' },
        { key: 'deleteKey', label: 'delete' },
        { key: 'limitActionsKey', label: 'limit Actions' }
      ]
    )

    const handleOp = (op: OpType, row: SetupLimitTableRow, index: number) => {
      switch (op) {
        case 'edit':
          row.flag = true
          currentTableItem.value = row
          break
        case 'delete':
          handleDelete(index)
          currentTableItem.value = undefined
          break
        case 'select':
          row.row_type = '*'
          row.flag = false
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
      }
    }
    const handleDelete = (index: number) => {
      data.value.splice(index, 1)
    }
    const handleOpCancel = (row: SetupLimitTableRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        data.value.splice(index, 1)
        return
      }
      row.row_type = '*'
      row.flag = false
    }

    onMounted(() => {
      getDataInfo()
    })

    expose({
      getData: () => data.value,
      handleSelect
    })

    return () => (
      <div class={[e('body', 'table', 'left'), 'cfg-setup']}>
        <div class='cfg-setup_table'>
          <wui-table height='100%' data={data.value} border onRow-contextmenu={handleRowMenu}>
            {{
              default: () => (
                <>
                  {columns.map(({ isPermanentFocus, ...params }) => (
                    <wui-table-column {...params}>
                      {{
                        default: ({ row }: any) => {
                          const clickHandler = isPermanentFocus
                            ? (ev: MouseEvent) => handleInputClick(row, params.prop, ev)
                            : cancelPermanentFocus
                          if (
                            row.flag &&
                            (params.prop === 'deadband' || params.prop === 'wait_sec')
                          ) {
                            const numberProps = {
                              min: 0,
                              max: 9999,
                              precision: 2,
                              placeholder: 'Please Input'
                            }
                            return (
                              <wui-input-number
                                v-model={row[params.prop]}
                                input-align='left'
                                clearable
                                controls={false}
                                {...numberProps}
                                onClick={clickHandler}
                              />
                            )
                          }
                          return row.flag ? (
                            <wui-input
                              onClick={clickHandler}
                              placeholder='Please Input'
                              v-model={row[params.prop]}
                            />
                          ) : (
                            row[params.prop]
                          )
                        }
                      }}
                    </wui-table-column>
                  ))}
                  <wui-table-column align='center' width='100' label='Op'>
                    {{
                      default: ({ row, $index }: any) => (
                        <TableTool.Op flag={row.flag} onOp={op => handleOp(op, row, $index)} />
                      )
                    }}
                  </wui-table-column>
                </>
              ),
              empty: () => <TableTool.Empty onCustom-contextmenu={handleTableAreaCtxMenu} />
            }}
          </wui-table>
        </div>
      </div>
    )
  }
})
