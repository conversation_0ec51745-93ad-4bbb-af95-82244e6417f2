import { BaseTableDefaultScope, BaseTableRow } from '@/renderer/components/TableTool'
import { LimitCounter, LimitParameter, AlarmRelay, CfgLimt, RateLimt } from '@wuk/cfg'
export type LimitTableRow = BaseTableRow<LimitParameter>
export type SetupLimitTableRow = BaseTableRow<CfgLimt>
export type SetupLimitRateTableRow = BaseTableRow<RateLimt>

export type SetupCounterRow = BaseTableRow<LimitCounter>
export type SetupCounterDeafultScope = BaseTableDefaultScope<SetupCounterRow>
export type SetupLimitExpose = {
  handleSelect: (val: string) => void
  getData: () => any
}

export type SetupLimitOption = {
  alarm_relay: AlarmRelay
  limit_counters: SetupCounterRow[]
  parameters: LimitTableRow[]
}

export type currentParameters = {
  parameter?: LimitTableRow
  index?: number
}

export type RateLimtPartial = Partial<RateLimt>

export type CfgLimtPartial = Partial<CfgLimt>
