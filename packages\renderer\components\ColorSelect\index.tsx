import { defineComponent, onMounted, ref, watch } from 'vue'

export default defineComponent({
  name: 'ColorSelect',
  props: {
    modelValue: {
      type: String,
      default: 'red'
    },
    placeholder: {
      type: String,
      default: 'Select color'
    }
  },
  setup(props, { slots, emit }) {
    const color = ref()

    const colorOptions = {
      Black: 'Black',
      Red: 'Red',
      Pink: 'Pink',
      Orange: 'Orange',
      Green: 'Green',
      Yellow: 'Yellow',
      Blue: 'Blue',
      <PERSON>gent<PERSON>: 'Magenta',
      <PERSON><PERSON>: '<PERSON><PERSON>',
      White: 'White',
      LightGray: 'LightGray',
      DarkGray: 'DarkGray',
      BurntOrange: 'BurntOrange',
      NewYellow: 'NewYellow',
      NewGreen: 'NewGreen',
      Charcoal: 'Charcoal'
    }

    onMounted(() => {
      color.value = props.modelValue
    })

    watch(color, newValue => {
      emit('update:modelValue', newValue)
    })

    watch(
      () => props.modelValue,
      newValue => {
        color.value = newValue
      }
    )
    return () => (
      <>
        <wui-select v-model={color.value} placeholder={props.placeholder}>
          {Object.entries(colorOptions).map(([key, value]) => (
            <wui-option label={value} value={key} />
          ))}
        </wui-select>
      </>
    )
  }
})
