<template>
  <div :class="b()">
    <TreeContent
      ref="treeContentRef"
      v-model:default-expanded-keys="defaultExpandedKeys"
      :tree-area-menu="treeMenu"
      :tree-data="treeData"
      @tree-area-contextmenu="handleTreeAreaCtxMenu"
      @tree-node-change="handleNodeChange"
      @tree-menu-init="handleTreeMenuInit"
    >
      <template v-if="mode === 'setup-crt'">
        <setup-crt
          v-model="curEditCrtInfo"
          @modify-quad="handleModifyQuad"
          @add-crt="isActive = true"
        />
      </template>
      <template v-else-if="mode === 'setup-quad'">
        <setup-quad
          :crt-index="curEditCrtInfo.crtIndex"
          :display-quad-option="curEditQuadInfo?.originQuadData"
          :quad-index="curEditQuadInfo?.index"
        />
      </template>
    </TreeContent>
    <MyDialog v-model="isActive" title="Add Crt Name" width="500px" @ok="handleAddCrt">
      <wui-form
        ref="crtFormRef"
        label-position="left"
        validate-box-style="fill"
        validate-msg-position="bottom"
        validate-box-gap="3"
        hide-required-asterisk
        status-icon
        :rules="rules"
        :class="e('popup')"
        :model="formModel"
      >
        <wui-form-item prop="name" style="width: 100%">
          <template #label>
            <span>Name</span>
          </template>
          <wui-input
            v-model="formModel.name"
            clearable
            placeholder="Please input"
            style="width: 100%; margin-left: 0; height: 32px"
          />
        </wui-form-item>
      </wui-form>
    </MyDialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { CtxType, TreeContent } from '@/renderer/components/TreeContent'
import { useHandler, useBizEngine } from '@/renderer/hooks'
import { BizEngine } from '@/renderer/logic'
import { useBem } from '@/renderer/hooks/bem'
import { WuiForm, WuiMessage } from '@wuk/wui'
import { AppMenuItem } from '@wuk/cfg'
import { SetupQuad } from './setupQuad'
import { SetupCrt, rules } from './setupCrt'
import styles from './index.module.scss'
import { DspTree, UpdateType } from './type'
import MyDialog from '@/renderer/components/dialog/index.vue'
const treeMenu = ref<AppMenuItem[]>([
  { key: 'AddKey', label: 'add' },
  { key: 'DeleteKey', label: 'Delete' }
])
const treeContentRef = ref<InstanceType<typeof TreeContent>>()
const { b, e } = useBem('dsp', styles)

const crtFormRef = ref<InstanceType<typeof WuiForm>>()
const displayPtr = useBizEngine()
const treeData = ref<DspTree[]>([])
const isActive = ref(false)
const curEditCrtInfo = ref<UpdateType>({
  name: '',
  id: '',
  crtIndex: -1,
  quadList: []
})
const defaultExpandedKeys = ref<string[]>([])
/**
 * @description: 获取tree 第一项 CRT的ID
 */
const treeFsItemId = computed(() => (treeData.value[0] ? treeData.value[0].id : ''))
const mode = ref<'setup-crt' | 'setup-quad' | ''>('setup-crt')
const curEditQuadInfo = ref<DspTree>()
const formModel = reactive({
  name: ''
})

const handleTreeMenuInit = (type: CtxType) => {
  if (type === 'Node') {
    treeMenu.value = [
      { key: 'AddKey', label: 'add' },
      { key: 'DeleteKey', label: 'Delete' }
    ]
  } else {
    treeMenu.value = [{ key: 'AddKey', label: 'add' }]
  }
}

/**
 * @description 处理tree右键菜单
 */
watch(
  () => treeData.value,
  treeData => {
    const menu = [{ key: 'AddKey', label: 'add' }]
    if (treeData.length !== 0) {
      menu.push({ key: 'DeleteKey', label: 'Delete' })
    }
    treeMenu.value = menu
  }
)

const handleModifyQuad = (id: string) => {
  treeContentRef.value?.treeRef?.setCurrentKey(id)
}

const handleNodeChange = (data: DspTree, node: any) => {
  const { level, parent } = node
  const { label, index, id } = data
  if (level === 1) {
    mode.value = 'setup-crt'
    curEditCrtInfo.value = {
      name: label,
      crtIndex: index,
      id: id as string,
      quadList: data.children || []
    }
  } else if (level === 2) {
    const { data: parentData } = parent
    mode.value = 'setup-quad'
    curEditQuadInfo.value = data
    curEditCrtInfo.value = {
      name: parentData.label,
      crtIndex: parentData.index,
      id: parentData.id as string,
      quadList: parentData.children || []
    }
  }
}
// 数据处理
const getDataInfo = async () => {
  const { list = [] } = (await displayPtr.value?.readDisplayOptions()) || {}
  treeData.value = list.map((item, index) => ({
    label: item.name,
    index: index,
    id: `${item.name}-${index}`,
    children: item.quads.map((row, rowIdx) => ({
      label: row.quad_name,
      index: rowIdx,
      id: `${item.name}-${row.quad_name}-${rowIdx}`,
      originQuadData: row
    }))
  }))
  if (treeData.value.length === 0) {
    curEditCrtInfo.value = {
      name: '',
      crtIndex: -1,
      id: '',
      quadList: []
    }
  }
}

const handleAddCrt = async () => {
  const valid = await crtFormRef.value?.validateField('name')
  if (!valid) return
  const res = await displayPtr.value?.addDisplayCrt(formModel.name)
  if (!res) return
  const len = treeData.value.length
  const id = `${formModel.name}-${len}`
  treeData.value.push({
    label: formModel.name,
    index: len,
    id,
    children: []
  })
  if (curEditCrtInfo.value.crtIndex === -1) {
    curEditCrtInfo.value = {
      name: formModel.name,
      crtIndex: len,
      id,
      quadList: []
    }
    handleModifyQuad(id)
  }
  formModel.name = ''
  isActive.value = false
}
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 70,
    grouping: true
  })
}

const handleTreeAreaCtxMenu = async (key: string, ...args: any) => {
  const [data, node] = args
  if (key === 'AddKey') {
    isActive.value = true
  } else if (key === 'DeleteKey') {
    if (!node) return
    const { crtIndex } = curEditCrtInfo.value
    const params: [number, number?] = node.level === 1 ? [data.index] : [crtIndex ?? -1, data.index]
    const res = await displayPtr.value?.removeDisplayQuad(...params)
    if (!res) return
    tipsMessage()
  }
}
useHandler(displayPtr, BizEngine.onDisplayOptionsChanged, getDataInfo)
onMounted(async () => {
  await getDataInfo()
  treeContentRef.value?.treeRef?.setCurrentKey(treeFsItemId.value)
  defaultExpandedKeys.value = [treeFsItemId.value as string]
})
</script>
