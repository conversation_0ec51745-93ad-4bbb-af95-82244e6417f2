import { TargetClass, RootCore } from '@/renderer/boots'
import { BizMain } from './types'
import {
  AppCustomer,
  AppEngine,
  AudoItem,
  CRSOptions,
  CfgVersion,
  CrtItem,
  DataFormatOptions,
  DialogProperty,
  CalcGroupable,
  Hardware,
  IApp,
  ICalc,
  ICfg,
  ISystem,
  PrintModeOptions,
  ResolutionItem,
  RgbItem,
  SystemOptions,
  CalcFile,
  CalsGroupItem
} from '@wuk/cfg'
import { rgba2string } from '@/renderer/utils'
import { LinkMap } from '@/renderer/utils/linkmap'

@TargetClass(BizMain, RootCore, true)
export default class MainImpl extends BizMain {
  private _customer?: AppCustomer
  private _customers: AppCustomer[]

  private _engine?: AppEngine
  private _engines: AppEngine[]

  private _version?: CfgVersion
  private _versions: CfgVersion[]

  private _colors: LinkMap<string, string, RgbItem>
  private _resolutions: LinkMap<string, string, ResolutionItem>

  constructor(key: string) {
    super(key)

    this._customers = []
    this._versions = []
    this._engines = []
    this._colors = new LinkMap<string, string, RgbItem>()
    this._resolutions = new LinkMap<string, string, ResolutionItem>()

    this.handleAppCustomers = this.handleAppCustomers.bind(this)
    this.handleAppVersions = this.handleAppVersions.bind(this)

    this.handleCurrentCustomer = this.handleCurrentCustomer.bind(this)
    this.handleCurrentVersion = this.handleCurrentVersion.bind(this)
    this.handleCurrentEngine = this.handleCurrentEngine.bind(this)

    this.handleMessageArrived = this.handleMessageArrived.bind(this)
    this.handleEnginesChanged = this.handleEnginesChanged.bind(this)
    this.handleSystemOptionChanged = this.handleSystemOptionChanged.bind(this)
    this.handlePrintModeChanged = this.handlePrintModeChanged.bind(this)
    this.handleDataFormatChanged = this.handleDataFormatChanged.bind(this)
    this.handleCRSOptionChanged = this.handleCRSOptionChanged.bind(this)
    this.handleCrtOptionChanged = this.handleCrtOptionChanged.bind(this)
    this.handleAudioOptionChanged = this.handleAudioOptionChanged.bind(this)
    this.handleDeviceOptionChanged = this.handleDeviceOptionChanged.bind(this)
    this.handleColorOptionChanged = this.handleColorOptionChanged.bind(this)
    this.handleCalcFileChanged = this.handleCalcFileChanged.bind(this)
    this.handleResolutionOptionChanged = this.handleResolutionOptionChanged.bind(this)
    this.handleCalcsOptionsChanged = this.handleCalcsOptionsChanged.bind(this)
  }

  async init() {
    await super.init()

    this.app.sdk?.on(IApp.OnEngine, this.handleCurrentCustomer)
    this.app.sdk?.on(IApp.OnCustomers, this.handleAppCustomers)
    this.app.sdk?.on(IApp.OnVersion, this.handleCurrentVersion)
    this.app.sdk?.on(IApp.OnVersions, this.handleAppVersions)

    this.app.sdk?.on(IApp.OnEngine, this.handleCurrentEngine)
    this.app.sdk?.on(IApp.OnEngines, this.handleEnginesChanged)

    this.app.sdk?.system.on(ISystem.OnChanged, this.handleSystemOptionChanged)
    this.app.sdk?.system.on(ISystem.OnPrintModes, this.handlePrintModeChanged)
    this.app.sdk?.system.on(ISystem.OnDataFormat, this.handleDataFormatChanged)
    this.app.sdk?.system.on(ISystem.OnCRSOptions, this.handleCRSOptionChanged)
    this.app.sdk?.system.on(ISystem.OnCrtOptions, this.handleCrtOptionChanged)
    this.app.sdk?.system.on(ISystem.OnAudioOptions, this.handleAudioOptionChanged)
    this.app.sdk?.system.on(ISystem.OnDeviceOptions, this.handleDeviceOptionChanged)
    this.app.sdk?.system.on(ISystem.OnColorOptions, this.handleColorOptionChanged)
    this.app.sdk?.system.on(ISystem.OnResolutionOptions, this.handleResolutionOptionChanged)

    this.app.sdk?.calc.on(ICalc.OnCalc, this.handleCalcFileChanged)
    this.app.sdk?.calc.on(ICalc.OnOptions, this.handleCalcsOptionsChanged)

    this.app.sdk?.cfg.on(ICfg.OnMessage, this.handleMessageArrived)

    this.reloadCustomers()
    this.reloadVersions()
    this.reloadEngines()
    this.readColorOptions()
    this.readResolutionOptions()

    const info = await this.app.sdk?.appInfo()
    this.log('MainImpl', 'app info=========={%1}', info)
  }

  get customer() {
    return this._customer
  }

  get version() {
    return this._version
  }

  get engine() {
    return this._engine
  }

  get colors() {
    return this._colors
  }

  get resolutions() {
    return this._resolutions
  }

  async changeCustomer(name: string) {
    const result = await this.app.sdk?.changeCustomer(name)
    return !!result
  }

  getVersions() {
    return this._versions
  }

  getCustomers(): AppCustomer[] {
    return this._customers
  }

  getEngines() {
    return this._engines
  }

  async importVersion(name: string) {
    const result = await this.app.sdk?.importVerison(name)
    return !!result
  }

  async exportVersion(comments: string) {
    const result = await this.app.sdk?.exportVerison(comments)
    return !!result
  }

  async getMessages() {
    const result = await this.app.sdk?.cfg.loadMessage()
    return result
  }

  async showOpenDialog(title: string, properties: DialogProperty[] = ['openFile']) {
    return (await this.app.sdk?.win.showOpenDialog(title, properties)) || ''
  }

  async uploadImage(path: string) {
    return (await this.app.sdk?.uploadImage(path)) || ''
  }
  async customerURL(path: string) {
    return (await this.app.sdk?.customerURL(path)) || ''
  }
  async imageURL(path: string) {
    return (await this.app.sdk?.customerURL(path)) || ''
  }

  async openEngine(name: string) {
    const result = await this.app.sdk?.openEngine(name)
    return !!result
  }

  async createEngine(name: string) {
    const result = await this.app.sdk?.createEngine(name)
    return !!result
  }

  async copyEngine(name: string, newName: string) {
    const result = await this.app.sdk?.copyEngine(name, newName)
    return !!result
  }
  async deleteEngine(name: string) {
    const result = await this.app.sdk?.deleteEngine(name)
    return !!result
  }

  // System Options
  async readSystemOptions() {
    const result = await this.app.sdk?.system.readSystemOptions()
    return result
  }
  async writeSystemOptions(val: Partial<SystemOptions>) {
    const result = await this.app.sdk?.system.writeSystemOptions(val)
    return !!result
  }

  // Print Modes
  async readPrintModes() {
    const result = await this.app.sdk?.system.readPrintModes()
    return result
  }
  async writePrintModes(val: Partial<PrintModeOptions>) {
    const result = await this.app.sdk?.system.writePrintModes(val)
    return !!result
  }

  // Data Formats
  async readDataFormat() {
    const result = await this.app.sdk?.system.readDataFormat()
    return result
  }
  async writeDataFormat(val: Partial<DataFormatOptions>) {
    const result = await this.app.sdk?.system.writeDataFormat(val)
    return !!result
  }

  // CRS Options
  async readCRSOptions() {
    const result = await this.app.sdk?.system.readCRSOptions()
    return result
  }
  async writeCRSOptions(val: Partial<CRSOptions>) {
    const result = await this.app.sdk?.system.writeCRSOptions(val)
    return !!result
  }

  // Crts Options
  async readCrtOptions() {
    const result = await this.app.sdk?.system.readCrtOptions()
    return result
  }
  async setCrtRate(val: number) {
    const result = await this.app.sdk?.system.setCrtRate(val)
    return !!result
  }
  async removeCrt(index: number) {
    const result = await this.app.sdk?.system.removeCrt(index)
    return !!result
  }
  async addCrt(val: CrtItem, index?: number) {
    const result = await this.app.sdk?.system.addCrt(val, index)
    return !!result
  }
  async modifyCrt(index: number, val: Partial<CrtItem>) {
    const result = await this.app.sdk?.system.modifyCrt(index, val)
    return !!result
  }

  // Audio Options
  async readAudioOptions() {
    const result = await this.app.sdk?.system.readAudioOptions()
    return result
  }
  async setHostAddress(val: string) {
    const result = await this.app.sdk?.system.setHostAddress(val)
    return !!result
  }
  async removeAudio(index: number) {
    const result = await this.app.sdk?.system.removeAudio(index)
    return !!result
  }
  async addAudio(val: AudoItem, index?: number) {
    const result = await this.app.sdk?.system.addAudio(val, index)
    return !!result
  }
  async modifyAudio(index: number, val: Partial<AudoItem>) {
    const result = await this.app.sdk?.system.modifyAudio(index, val)
    return !!result
  }

  // Device Options
  async readDeviceOptions() {
    const result = await this.app.sdk?.system.readDeviceOptions()
    return result
  }
  async removeDevice(index: number) {
    const result = await this.app.sdk?.system.removeDevice(index)
    return !!result
  }
  async addDevice(val: Hardware, index?: number) {
    const result = await this.app.sdk?.system.addDevice(val, index)
    return !!result
  }
  async modifyDevice(index: number, val: Partial<Hardware>) {
    const result = await this.app.sdk?.system.modifyDevice(index, val)
    return !!result
  }

  // Color Options
  async readColorOptions() {
    const result = await this.app.sdk?.system.readColorOptions()
    this._colors.clear()
    result?.list?.forEach(item => {
      const key = item.name
      const val = rgba2string([item.r, item.g, item.b, item.a])
      this._colors.put(key, val, item)
    })

    return result
  }
  async removeColor(index: number) {
    const result = await this.app.sdk?.system.removeColor(index)
    return !!result
  }
  async addColor(val: RgbItem, index?: number) {
    const result = await this.app.sdk?.system.addColor(val, index)
    return !!result
  }
  async modifyColor(index: number, val: Partial<RgbItem>) {
    const result = await this.app.sdk?.system.modifyColor(index, val)
    return !!result
  }

  async readResolutionOptions() {
    const result = await this.app.sdk?.system.readResolutionOptions()
    this._resolutions.clear()
    result?.list?.forEach(item => {
      const key = item.name
      const val = `${item.width}x${item.height}`
      this._resolutions.put(key, val, item)
    })

    return result
  }

  async removeResolution(index: number) {
    const result = await this.app.sdk?.system.removeResolution(index)
    return !!result
  }
  async addResolution(val: ResolutionItem, index?: number) {
    const result = await this.app.sdk?.system.addResolution(val, index)
    return !!result
  }
  async modifyResolution(index: number, val: Partial<ResolutionItem>) {
    const result = await this.app.sdk?.system.modifyResolution(index, val)
    return !!result
  }

  async readCalcsOptions(): Promise<Array<CalsGroupItem>> {
    const { files = [] } = (await this.app.sdk?.calc.readOptions()) || {}
    return files.map(item => ({
      file: item.file,
      group_name: item.name,
      type: item.type
    }))
  }

  async removeCalcFile(index: number): Promise<boolean> {
    const result = await this.app.sdk?.calc.removeFile(index)
    return !!result
  }
  async addCalcFile(item: CalsGroupItem, index?: number): Promise<boolean> {
    const result = await this.app.sdk?.calc.addFile(
      {
        file: item.file,
        name: item.group_name,
        type: item.type,
        depends: []
      },
      index
    )
    return !!result
  }
  async modifyCalcFile(index: number, item: Partial<CalsGroupItem>): Promise<boolean> {
    const result = await this.app.sdk?.calc.modifyFile(index, {
      file: item.file,
      name: item.group_name,
      type: item.type,
      depends: []
    })
    return !!result
  }

  async loadCalc(file: string): Promise<CalcGroupable | undefined> {
    const result = await this.app.sdk?.calc.loadCalc(file)
    return result
  }

  async modifyCalc(file: string, val: Partial<CalcGroupable>): Promise<boolean> {
    const result = await this.app.sdk?.calc.modifyCalc(file, val)
    return !!result
  }

  async saveCalc(file: string): Promise<boolean> {
    const result = await this.app.sdk?.calc.saveCalc(file)
    return !!result
  }

  async loadCalcText(file: string): Promise<string | undefined> {
    const result = await this.app.sdk?.calc.loadCalcText(file)
    return result
  }
  async saveCalcText(file: string, text: string): Promise<boolean> {
    const result = await this.app.sdk?.calc.saveCalcText(file, text)
    return !!result
  }

  private async reloadCustomers() {
    this._customer = await this.app.sdk?.getCustomer()
    this._customers = (await this.app.sdk?.loadCustomers()) || []

    this.emit(BizMain.onCustomersChanged)
  }

  private async reloadVersions() {
    this._version = await this.app.sdk?.getVersion()
    this._versions = (await this.app.sdk?.loadVersions()) || []

    this.emit(BizMain.onVersionsChanged)
  }

  private async handleAppCustomers() {
    this.reloadCustomers()
  }

  private handleAppVersions() {
    this.reloadVersions()
  }

  private handleMessageArrived() {
    this.emit(BizMain.onMessageArrived)
  }

  private handleEnginesChanged() {
    this.reloadEngines()
  }

  private handleSystemOptionChanged() {
    this.emit(BizMain.onSystemOptionChanged)
  }

  private handlePrintModeChanged() {
    this.emit(BizMain.onPrintModeChanged)
  }

  private handleCRSOptionChanged() {
    this.emit(BizMain.onCRSOptionChanged)
  }

  private handleCrtOptionChanged() {
    this.emit(BizMain.onCrtOptionChanged)
  }

  private handleAudioOptionChanged() {
    this.emit(BizMain.onAudioOptionChanged)
  }

  private handleColorOptionChanged() {
    this.readColorOptions()
    this.emit(BizMain.onColorOptionChanged)
  }

  private handleResolutionOptionChanged() {
    this.readResolutionOptions()
    this.emit(BizMain.onResolutionOptionChanged)
  }

  private handleDataFormatChanged() {
    this.emit(BizMain.onDataFormatChanged)
  }

  private handleDeviceOptionChanged() {
    this.emit(BizMain.onDeviceOptionChanged)
  }

  private handleCalcFileChanged(file: string) {
    this.emit(BizMain.onCalcFileChanged, file)
  }

  private handleCalcsOptionsChanged() {
    this.emit(BizMain.onCalcsOptionsChanged)
  }

  private handleCurrentCustomer() {
    this.reloadCustomers()
  }

  private handleCurrentVersion() {
    this.reloadVersions()
  }

  private handleCurrentEngine() {
    this.reloadEngines()
  }

  private async reloadEngines() {
    this._engine = await this.app.sdk?.getEngine()
    this._engines = (await this.app.sdk?.loadEngines()) || []

    this.emit(BizMain.onEnginesChanged)
  }
}
