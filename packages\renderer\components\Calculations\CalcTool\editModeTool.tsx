import { defineComponent, PropType, useModel } from 'vue'
import $styles from './index.module.scss'
import { useBem } from '@/renderer/hooks'
export enum EditMode {
  Table = 0,
  Text = 1
}
export const EditModeTool = defineComponent({
  name: 'EditModeTool',
  props: {
    editMode: {
      type: Number as PropType<EditMode>,
      default: EditMode.Table
    }
  },
  emits: ['update:editMode'],
  setup(props) {
    const { e } = useBem('calc-tool', $styles)
    const editMode = useModel(props, 'editMode')
    return () => (
      <div class={e('editmode')}>
        <wui-radio-group v-model={editMode.value}>
          <wui-radio-button label='Row Column List' value={EditMode.Table} />
          <wui-radio-button label='Text Window' value={EditMode.Text} />
        </wui-radio-group>
      </div>
    )
  }
})
