import {
  computed,
  defineAsyncComponent,
  defineComponent,
  inject,
  ref,
  useModel,
  watchEffect
} from 'vue'
import {
  WuiInput,
  WuiSelect,
  WuiOption,
  WuiDatePicker,
  WuiForm,
  WuiFormItem,
  WuiRadioGroup,
  WuiRadioButton,
  WuiScrollbar,
  WuiMessageBox,
  WuiMessage
} from '@wuk/wui'
import { useBem } from '../../../../hooks/bem'
import MyDialog from '../../../dialog/index.vue'
import $styles from './index.module.scss'
import { calcTableContextKey } from '../constants'
import { transformToCoordinates, useParameterDialog } from '@/renderer/utils/common'
import { EditMode, EditModeTool } from '../../CalcTool/editModeTool.tsx'
import { EditRowCodeExpose } from './editRowCode.tsx'
import { validDataCode } from '@/renderer/utils/rules.ts'
import { useBizEngine } from '@/renderer/hooks/boots.ts'
const EditRowTable = defineAsyncComponent(() => import('./editRowTable.tsx'))
const EditRowCode = defineAsyncComponent(() => import('./editRowCode.tsx'))
export enum TableType {
  Polynomial = 0,
  Constant = 1,
  '2D' = 2,
  '3D' = 3,
  'DynamicX' = 4
}
export type FormModel = {
  expression: string
  reviewDate: string
  manualDate: string
  manualRevision: string
  comments: string
  tableType: number
  storeToDb: number
  dynamicExpr: number
  interpolation: number
  extrapolation: number
  output: string
  xInput: string
  yInput: string
  polyDegree: number
  coeff: number[]
  minpoly: number
  maxpoly: number
  constant: number
  corrData: { coord1: number; coord2: number; coord3?: number }[]
}
const handleSelectParamter = (formModel: Record<string, any>, key: string) => {
  return async () => {
    const res = await useParameterDialog()
    formModel[key] = res
  }
}
const { b, e, m } = useBem('editrow', $styles)
export const PolyList = ({ formModel }: { formModel: FormModel }) => {
  if (formModel.tableType !== TableType.Polynomial) return null
  return (
    <>
      <div class={[e('row')]}>
        <wui-form-item label='Polynomial Degree'>
          <wui-select v-model={formModel.polyDegree}>
            {Array.from({ length: 10 }).map((_, idx) => (
              <>
                <wui-option label={idx + 1} value={idx + 1} />
              </>
            ))}
          </wui-select>
        </wui-form-item>
        <wui-form-item style='margin-left: 87px' label='Polynomial Input'>
          <wui-button onClick={handleSelectParamter(formModel, 'xInput')}>
            {formModel.xInput || 'None'}
          </wui-button>
        </wui-form-item>
      </div>
      <div class={[e('row'), m('between', 'row')]}>
        <div>
          {Array.from({ length: formModel.polyDegree }).map((_, idx) => {
            const base = !idx ? '' : `* X`
            const power = idx <= 1 ? '' : `**${idx}`
            const label = `Coeff ${idx} ${base}${power}`
            return (
              <wui-form-item>
                {{
                  label: () => <span>{label}</span>,
                  default: () => <wui-input v-model={formModel.coeff[idx]} type='number' />
                }}
              </wui-form-item>
            )
          })}
        </div>
        <div>
          <wui-form-item label='Min X for Polynomial'>
            <wui-input v-model={formModel.minpoly} type='number' />
          </wui-form-item>
          <wui-form-item label='Max X for Polynomial'>
            <wui-input v-model={formModel.maxpoly} type='number' />
          </wui-form-item>
        </div>
      </div>
    </>
  )
}
export const ConstantList = ({ formModel }: { formModel: FormModel }) => {
  if (formModel.tableType !== TableType.Constant) return null
  return (
    <>
      <wui-form-item label='Constant Value'>
        <wui-input type='number' v-model={formModel.constant} />
      </wui-form-item>
    </>
  )
}
export const twoDList = ({ formModel }: { formModel: FormModel }) => {
  const extrapOptions = [
    { label: 'None', value: 0 },
    { label: 'Constant Value', value: 1 },
    { label: 'Capped', value: 2 },
    { label: 'Extrapolate', value: 3 }
  ]
  const interpOptions = [
    { label: 'Linear', value: 0 },
    { label: '3 Point Parabolic', value: 1 }
  ]
  if (formModel.tableType !== TableType['2D']) return null
  return (
    <div class={[e('row')]}>
      <WuiFormItem labelWidth='120' label='Interpolation'>
        <WuiSelect v-model={formModel.interpolation} class={e('select')}>
          {interpOptions.map(({ label, value }) => (
            <WuiOption label={label} value={value} />
          ))}
        </WuiSelect>
      </WuiFormItem>
      <WuiFormItem label='Extrapolation Method'>
        <WuiSelect v-model={formModel.extrapolation} class={e('select')}>
          {extrapOptions.map(({ label, value }) => (
            <WuiOption label={label} value={value} />
          ))}
        </WuiSelect>
      </WuiFormItem>
    </div>
  )
}
export default defineComponent({
  name: 'EditRow',
  props: {
    showEditRow: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:showEditRow'],
  setup(props) {
    const calcTableContext = inject(calcTableContextKey)
    if (!calcTableContext) return
    const showEditRow = useModel(props, 'showEditRow')
    const editRowCodeRef = ref<EditRowCodeExpose>()
    const editMode = ref(EditMode.Table)
    const bizEngine = useBizEngine()
    // 表单数据
    const formModel = ref<FormModel>({
      expression: '',
      reviewDate: '',
      manualDate: '',
      manualRevision: '',
      comments: '',
      tableType: 0,
      storeToDb: 0,
      dynamicExpr: 0,
      interpolation: 0,
      extrapolation: 0,
      output: 'None',
      xInput: 'None',
      yInput: 'None',
      polyDegree: 0,
      coeff: [] as number[],
      minpoly: 0,
      maxpoly: 0,
      constant: 0,
      corrData: []
    })
    const title = computed(() => {
      const { curTableEqInfo, curTableGroupInfo } = calcTableContext
      console.log(curTableEqInfo.item, 'curTableEqInfo.item')
      return `TableGroup: ${curTableGroupInfo.groupName}  Table: ${curTableEqInfo.item.name}`
    })
    const tableTypeOptions = [
      { label: 'Polynomial', value: TableType.Polynomial },
      { label: 'Constant', value: TableType.Constant },
      { label: '2D', value: TableType['2D'] },
      { label: '3D', value: TableType['3D'] },
      { label: 'Dynamic X', value: TableType.DynamicX }
    ]
    const isPolyAndConst = computed(() =>
      [TableType.Constant, TableType.Polynomial].includes(formModel.value.tableType)
    )
    const isThreeD = computed(() => formModel.value.tableType === TableType['3D'])

    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90,
        grouping: true
      })
    }

    watchEffect(() => {
      const { item, tableLib } = calcTableContext.curTableEqInfo
      const {
        exp = '',
        revdate = '',
        mandate = '',
        manrev = '',
        comment = '',
        type = TableType.Polynomial,
        save = 0,
        dynexp = 0,
        interp = 0,
        extrap = 0,
        xparam_id = '',
        yparam_id = '',
        name = '',
        size = 0,
        coeff = [],
        minpoly = 0,
        maxpoly = 0,
        constant = 0
      } = item
      console.log(calcTableContext.curTableEqInfo)
      formModel.value = {
        expression: exp,
        reviewDate: revdate,
        manualDate: mandate,
        manualRevision: manrev,
        comments: comment,
        tableType: type,
        storeToDb: save,
        dynamicExpr: dynexp,
        interpolation: interp,
        extrapolation: extrap,
        output: name,
        xInput: xparam_id,
        yInput: yparam_id,
        polyDegree: size,
        coeff,
        minpoly,
        maxpoly,
        constant,
        corrData: tableLib ? transformToCoordinates(tableLib.data, tableLib.dim) : []
      }
    })

    const handleSubmit = async () => {
      const { curTableEqInfo, curTableGroupInfo } = calcTableContext
      const { tableLib, item, tableIndex } = curTableEqInfo
      const { groupName } = curTableGroupInfo
      const tableName = item.name as string
      if (!isPolyAndConst.value) {
        const dim = isThreeD.value ? 3 : 2
        let newData: number[] = []
        if (editMode.value === EditMode.Text) {
          const {
            valid,
            message,
            data = []
          } = validDataCode(editRowCodeRef.value?.codeTxt || '', dim)
          if (!valid) {
            WuiMessageBox.alert(message, 'Result', {
              confirmButtonText: 'OK'
            })
            return
          }
          newData = data
        } else {
          newData = formModel.value.corrData.reduce((pre, cur) => {
            const { coord1, coord2, coord3 } = cur
            pre.push(coord1, coord2)
            coord3 && pre.push(coord3)
            return pre
          }, [] as number[])
        }
        const fnName = tableLib ? 'modifyTableLib' : 'addTableLib'
        const libRes = await bizEngine.value?.[fnName](groupName, tableName, {
          name: tableName,
          dim,
          data: newData
        })
        const {
          expression,
          reviewDate,
          manualDate,
          manualRevision,
          comments,
          tableType,
          storeToDb,
          dynamicExpr,
          interpolation,
          extrapolation,
          output,
          xInput,
          yInput,
          polyDegree,
          coeff,
          minpoly,
          maxpoly,
          constant
        } = formModel.value
        const tableRes = await bizEngine.value?.modifyTable(groupName, tableIndex, {
          ...item,
          exp: expression,
          revdate: reviewDate,
          comment: comments,
          mandate: manualDate,
          manrev: manualRevision,
          type: tableType,
          save: storeToDb,
          dynexp: dynamicExpr,
          interp: interpolation,
          extrap: extrapolation,
          name: output,
          xparam_id: xInput,
          yparam_id: yInput,
          size: polyDegree,
          coeff,
          minpoly,
          maxpoly,
          constant
        })
        if (!libRes || !tableRes) return
        showEditRow.value = false
        tipsMessage()
      } else {
        const res = await bizEngine.value?.removeTableLib(groupName, tableName)
        if (!res) return
        tipsMessage()
      }
    }
    return () => (
      <MyDialog
        v-model={showEditRow.value}
        title={title.value}
        width='980px'
        contentStyle={{ padding: '15px 15px 0' }}
        onOk={handleSubmit}>
        <WuiScrollbar class={b()}>
          <WuiForm class={e('form')} labelSuffix=':' label-width='180px' label-position='left'>
            <div class={e('row')}>
              <WuiFormItem label='Expression'>
                <WuiInput v-model={formModel.value.expression} class={e('input')} />
              </WuiFormItem>
              <WuiFormItem label='Review Date'>
                <WuiDatePicker
                  disabled
                  v-model={formModel.value.reviewDate}
                  type='date'
                  format='dd MMM YYYY'
                  class={e('input')}
                />
              </WuiFormItem>
              <WuiFormItem label='Manual Date'>
                <WuiDatePicker
                  v-model={formModel.value.manualDate}
                  type='date'
                  format='DD MMM YYYY'
                  class={e('input')}
                />
              </WuiFormItem>
              <WuiFormItem label='Manual Revision'>
                <WuiInput v-model={formModel.value.manualRevision} class={e('input')} />
              </WuiFormItem>
            </div>
            <WuiFormItem labelPosition='top' label='Comments/Reference' class={e('textarea-item')}>
              <WuiInput
                v-model={formModel.value.comments}
                type='textarea'
                rows={2}
                class={e('textarea')}
              />
            </WuiFormItem>
            <div class={e('row')}>
              <WuiFormItem labelWidth='120' label='Table Type'>
                <WuiSelect v-model={formModel.value.tableType} class={e('select')}>
                  {tableTypeOptions.map(({ label, value }) => (
                    <WuiOption label={label} value={value} />
                  ))}
                </WuiSelect>
              </WuiFormItem>
              <WuiFormItem label='Store to Database'>
                <WuiRadioGroup v-model={formModel.value.storeToDb} class={e('radio-group')}>
                  <WuiRadioButton label='No' value={0} />
                  <WuiRadioButton label='Yes' value={1} />
                </WuiRadioGroup>
              </WuiFormItem>
              <WuiFormItem label='Dynamic Expression'>
                <WuiRadioGroup v-model={formModel.value.dynamicExpr} class={e('radio-group')}>
                  <WuiRadioButton label='No' value={0} />
                  <WuiRadioButton label='Yes' value={1} />
                </WuiRadioGroup>
              </WuiFormItem>
            </div>
            <twoDList formModel={formModel.value} />
            <PolyList formModel={formModel.value} />
            <ConstantList formModel={formModel.value} />
            {!isPolyAndConst.value && (
              <div class={e('table-section')}>
                <wui-form-item label-width='100' label='Edit Mode'>
                  <EditModeTool v-model:editMode={editMode.value} />
                </wui-form-item>
                {editMode.value === EditMode.Table ? (
                  <EditRowTable formModel={formModel.value} />
                ) : (
                  <EditRowCode ref={editRowCodeRef} formModel={formModel.value} />
                )}
              </div>
            )}
          </WuiForm>
        </WuiScrollbar>
      </MyDialog>
    )
  }
})
