<template>
  <MyDialog v-model="isActive" title="PLC Calculation Editor" width="700px" @ok="onSave">
    <div :class="e('body')">
      <div style="margin-bottom: 16px">
        <!-- <div>Signal: {{ signal }}</div> -->
        <div>Source: {{ source }}</div>
      </div>
      <div style="display: flex; align-items: center; margin-bottom: 16px">
        <span style="margin-right: 16px">Polynomial degree</span>
        <wui-select v-model="degree" style="width: 80px; margin-right: 32px">
          <wui-option v-for="n in 10" :key="n" :value="n" :label="n" />
        </wui-select>
        <span style="margin-right: 8px">X Range</span>
        <wui-input v-model="model.min" placeholder="Min" style="width: 120px; margin-right: 8px" />
        <wui-input v-model="model.max" placeholder="Max" style="width: 120px" />
      </div>
      <div :class="e('calc')">
        <template v-for="n in degree" :key="n">
          <wui-input v-model="coeffs[degree - n]" style="width: 100px" />
          <span v-if="degree - n > 0" style="margin: 0 4px"
            >X<sup>{{ degree - n }}</sup> ×</span
          >
        </template>
        <wui-input v-model="coeffs[degree]" style="width: 100px" />
      </div>
    </div>
  </MyDialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import $styles from '../index.module.scss'
import { useBem } from '@/renderer/hooks/bem'

const { e } = useBem('plc-signal-poly', $styles)

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  calibData: { type: Object, default: () => ({}) },
  source: { type: String, default: '' }
})
const emit = defineEmits(['update:modelValue', 'save'])

const isActive = ref(props.modelValue)

const model = reactive({
  data: [] as any[],
  min: Number,
  max: Number,
  type: String
})

const degree = ref(10)

const coeffs = reactive(Array.from({ length: 11 }, () => '1.00000'))

watch(isActive, v => emit('update:modelValue', v))

watch(degree, val => {
  if (val + 1 > coeffs.length) {
    for (let i = coeffs.length; i < val + 1; i++) {
      coeffs.push('1.00000')
    }
  } else if (val + 1 < coeffs.length) {
    coeffs.splice(val + 1)
  }
})

const getDataInfo = async () => {
  model.data = props.calibData.raw_type === 1 ? props.calibData.data : []
  model.min = props.calibData.min
  model.max = props.calibData.max
  model.type = props.calibData.type
  degree.value = model.data.length
  coeffs.splice(0, coeffs.length, ...model.data.map(String))
  if (coeffs.length === 0) {
    coeffs.splice(0, coeffs.length, ...Array.from({ length: 11 }, () => '1.00000'))
    degree.value = 10
  }
}

const onSave = () => {
  model.data = coeffs.map(Number)
  emit('save', model)
  isActive.value = false
}

onMounted(() => {
  getDataInfo()
})
</script>
