<template>
  <MyDialog v-model="isActive" title="PLC Calculation Editor" width="700px" @ok="handleSave">
    <div :class="e('body')">
      <div style="margin-bottom: 16px">
        <!-- <div>Signal: {{ signal }}</div> -->
        <div>Source: {{ source }}</div>
      </div>
      <div style="display: flex; align-items: center; margin-bottom: 16px">
        <span style="margin-right: 16px">Polynomial degree</span>
        <wui-select v-model="degree" style="width: 80px; margin-right: 32px">
          <wui-option v-for="n in POLY_MAX_DEGREE" :key="n" :value="n" :label="n" />
        </wui-select>
        <span style="margin-right: 8px">X Range</span>
        <wui-input-number
          v-model="model.min"
          placeholder="Min"
          :controls="false"
          style="width: 120px; margin-right: 8px"
          @blur="validateMinMaxInput"
        />
        <wui-input-number
          v-model="model.max"
          :controls="false"
          placeholder="Max"
          style="width: 120px"
          @blur="validateMinMaxInput"
        />
      </div>
      <div :class="e('calc')">
        <template v-for="n in degree" :key="n">
          <wui-input
            v-model="coeffs[degree - n]"
            style="width: 100px"
            @blur="validateCoefficient(degree - n)"
          />
          <span v-if="degree - n > 0" style="margin: 0 4px"
            >X<sup>{{ degree - n }}</sup> ×</span
          >
        </template>
        <wui-input
          v-model="coeffs[degree]"
          style="width: 100px"
          @blur="validateCoefficient(degree)"
        />
      </div>
    </div>
  </MyDialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import $styles from '../index.module.scss'
import { useBem } from '@/renderer/hooks/bem'
import { WuiMessage } from '@wuk/wui'
import {
  PolyDialogProps,
  PolyDialogModel,
  CalibDataType,
  POLY_MAX_DEGREE,
  DEFAULT_COEFF_VALUE
} from '../../type'

const { e } = useBem('plc-signal-poly', $styles)

const props = withDefaults(defineProps<PolyDialogProps>(), {
  modelValue: false,
  calibData: () => ({
    type: CalibDataType.None,
    min: 0,
    max: 0,
    data: [],
    raw_type: CalibDataType.None
  }),
  source: ''
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  save: [model: PolyDialogModel]
}>()

const isActive = ref(props.modelValue)
const degree = ref(POLY_MAX_DEGREE)

const model = reactive<PolyDialogModel>({
  data: [],
  min: 0,
  max: 0,
  type: CalibDataType.Polynomial
})

const coeffs = reactive<string[]>(
  Array.from({ length: POLY_MAX_DEGREE + 1 }, () => DEFAULT_COEFF_VALUE)
)

watch(isActive, value => emit('update:modelValue', value))

watch(degree, newDegree => {
  const requiredLength = newDegree + 1
  if (requiredLength > coeffs.length) {
    for (let i = coeffs.length; i < requiredLength; i++) {
      coeffs.push(DEFAULT_COEFF_VALUE)
    }
  } else if (requiredLength < coeffs.length) {
    coeffs.splice(requiredLength)
  }
})

const initializeData = (): void => {
  const isPolyData = props.calibData.raw_type === CalibDataType.Polynomial
  model.min = props.calibData.min
  model.max = props.calibData.max
  model.type = props.calibData.type
  if (isPolyData && props.calibData.data.length > 0) {
    model.data = [...props.calibData.data]
    degree.value = Math.min(props.calibData.data.length - 1, POLY_MAX_DEGREE)
    coeffs.splice(0, coeffs.length, ...props.calibData.data.map(num => num.toFixed(4)))
  } else {
    resetToDefaults()
  }
}

const resetToDefaults = (): void => {
  degree.value = POLY_MAX_DEGREE

  coeffs.splice(
    0,
    coeffs.length,
    ...Array.from({ length: POLY_MAX_DEGREE + 1 }, () => DEFAULT_COEFF_VALUE)
  )
}

const validateMinMaxInput = (): void => {
  if (isNaN(model.min) || isNaN(model.max)) {
    WuiMessage({
      message: 'Min and Max values must be valid numbers',
      type: 'warning',
      offset: 90
    })
    return
  }

  if (model.min >= model.max) {
    WuiMessage({
      message: 'Minimum value must be less than maximum value',
      type: 'warning',
      offset: 90
    })
  }
}

const validateCoefficient = (index: number): void => {
  const value = coeffs[index]
  const numValue = Number(value)

  if (value !== '' && isNaN(numValue)) {
    WuiMessage({
      message: 'Coefficient must be a valid number',
      type: 'warning',
      offset: 90
    })
    coeffs[index] = DEFAULT_COEFF_VALUE
    return
  }

  if (value !== '' && (numValue < model.min || numValue > model.max)) {
    WuiMessage({
      message: `Coefficient value (${numValue}) must be between ${model.min} and ${model.max}`,
      type: 'warning',
      offset: 90
    })
  }
}

const validateRange = (): boolean => {
  if (model.min >= model.max) {
    WuiMessage({
      message: 'Minimum value must be less than maximum value',
      type: 'error',
      offset: 90
    })
    return false
  }
  return true
}

const validateAllCoefficients = (): boolean => {
  for (let i = 0; i <= degree.value; i++) {
    const value = coeffs[i]
    const numValue = Number(value)

    if (value !== '' && isNaN(numValue)) {
      WuiMessage({
        message: `Coefficient at position ${i} must be a valid number`,
        type: 'error',
        offset: 90
      })
      return false
    }

    if (value !== '' && (numValue < model.min || numValue > model.max)) {
      WuiMessage({
        message: `Coefficient at position ${i} (${numValue}) must be between ${model.min} and ${model.max}`,
        type: 'error',
        offset: 90
      })
      return false
    }
  }
  return true
}

const handleSave = (): void => {
  if (!validateRange()) {
    return
  }

  model.data = coeffs.slice(0, degree.value + 1).map(Number)
  emit('save', model)
  isActive.value = false
}

onMounted(() => {
  initializeData()
})
</script>
