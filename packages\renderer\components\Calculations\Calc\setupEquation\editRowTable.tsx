import { useBem, useTableCommonMenu } from '@/renderer/hooks'
import { defineComponent, inject, PropType, toRef, useModel } from 'vue'
import $styles from './index.module.scss'
import {
  EquationTableRow,
  EquationTableRowKey,
  EquationTableRowScope,
  ModelType,
  EditRowTableExpose
} from './type'
import { Arrayable } from '@vueuse/core'
import { FormItemRule, WuiForm, WuiMessage, WuiMessageBox } from '@wuk/wui'
import TableTool, { isAddOrInsertType, OpType, RowType } from '@/renderer/components/TableTool'
import { CalcGroupable, CalcLineType, CalcWhenToExcuteType } from '@wuk/cfg'
import { calcContextKey } from '../constants'
import { watchEffect } from 'vue'
import { calcEqRules } from '../hooks'

export default defineComponent({
  name: 'EditRowTable',
  props: {
    model: {
      type: Object as PropType<ModelType>,
      default: () => ({})
    },
    eqFormRef: {
      type: Object as PropType<InstanceType<typeof WuiForm>>,
      default: undefined
    },
    handleModifyCalc: {
      type: Function as PropType<() => Promise<boolean | undefined>>,
      required: true
    }
  },
  emits: ['input-click', 'update:model'],
  setup(props, { emit, expose }) {
    const calcContext = inject(calcContextKey)
    if (!calcContext) return
    const { b, e } = useBem('setup-equation', $styles)
    const model = useModel(props, 'model')
    let originCalcGroupable: CalcGroupable | undefined
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90,
        grouping: true
      })
    }
    const createRow = (row_type: RowType, flag = true) => ({
      flag,
      row_type,
      comments: [],
      name: '',
      type: CalcLineType.kField,
      value: '',
      units: ''
    })
    const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
      toRef(model.value, 'list'),
      async (key, ...args) => {
        const { row, rowIndex } = args[0]
        switch (key) {
          case 'addKey':
            model.value.list.push(createRow('add'))
            break
          case 'insertKey':
            model.value.list.splice(rowIndex + 1, 0, createRow('insert'))
            break
          case 'modifyKey':
            handleOp('edit', row, rowIndex)
            break
          case 'deleteKey':
            handleOp('delete', row, rowIndex)
            break
        }
      }
    )

    const handleOp = (op: OpType, row: EquationTableRow, index: number) => {
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handleDelete(index)
          break
        case 'select':
          handleOpSelect(row, index)
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
      }
    }

    const handleOpSelect = async (row: EquationTableRow, index: number) => {
      const valid = await props.eqFormRef?.validateField(`list.${index}.name`)
      if (!valid) return
      const res = await props.handleModifyCalc()
      console.log(res, 'resresres')
      if (!res) return
      row.row_type = '*'
      row.flag = false
      tipsMessage()
    }

    /**
     * @description handle op delete
     */
    async function handleDelete(index: number) {
      model.value.list.splice(index, 1)
      const res = await props.handleModifyCalc()
      if (!res) return
      tipsMessage()
    }
    /**
     * @description handle op cancel
     */
    const handleOpCancel = (row: EquationTableRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.value.list.splice(index, 1)
        return
      }
      if (!originCalcGroupable) return
      const { name, type, comments, value } = originCalcGroupable.lines[index]
      row.name = name
      row.value = value
      row.comments = comments
      row.units = ''
      row.type = type
      row.flag = false
    }

    /**
     * @description reset form
     */
    const handleLoadCalc = async () => {
      const { groupNodeIndex = -1, children = [] } = calcContext.curEditCalcInfo
      const { originData } = children[groupNodeIndex] || {}
      if (!originData) return
      const originCalcGroupable = await calcContext.bizCalcs.loadCalc?.(
        originData.file,
        originData.type
      )
      const { excute = CalcWhenToExcuteType.kNone, lines = [] } = originCalcGroupable || {}
      model.value.when_to_excute = excute
      model.value.list = lines
        .filter(({ type }) => type === CalcLineType.kField)
        .map(line => ({
          ...line,
          units: '',
          flag: false,
          row_type: '*'
        }))
    }

    /**
     * @description save option
     */
    const handleSave = async () => {
      const onSave = async () => {
        const { curEditCalcInfo, bizCalcs, changeTreeNode } = calcContext
        const { groupNodeIndex = -1, children = [] } = curEditCalcInfo
        const originData = children[groupNodeIndex].originData
        const res = await bizCalcs.saveCalc?.(originData.file, originData.type)
        if (!res) return
        changeTreeNode(curEditCalcInfo.calcId)
      }
      const isNoSave = model.value.list.some(item => item.flag)
      if (isNoSave) {
        WuiMessageBox.confirm(
          'Data has not been fully saved. Do you want to continue submitting the saved data?',
          'Warning',
          {
            confirmButtonText: 'OK',
            cancelButtonText: 'Close',
            type: 'warning',
            draggable: true,
            showClose: false
          }
        )
          .then(async () => {
            await onSave()
          })
          .catch(() => {})
      } else {
        await onSave()
      }
    }

    watchEffect(() => {
      handleLoadCalc()
    })

    expose<EditRowTableExpose>({
      handleSave
    })

    /**
     * @description column render
     */
    const RenderTableColumn = ({
      prop,
      label,
      rules
    }: {
      prop: EquationTableRowKey
      label: string
      rules?: Arrayable<FormItemRule>
    }) => {
      return (
        <>
          <wui-table-column prop={prop} label={label} align='center'>
            {{
              default: ({ row, $index }: EquationTableRowScope) => (
                <>
                  {row.flag ? (
                    rules ? (
                      <wui-form-item prop={`list.${$index}.${prop}`} rules={rules}>
                        <wui-input
                          onClick={(ev: MouseEvent) => emit('input-click', row, prop, ev)}
                          placeholder={`Please input ${prop}`}
                          v-model={row[prop]}
                          clearable
                        />
                      </wui-form-item>
                    ) : (
                      <wui-input
                        onClick={(ev: MouseEvent) => emit('input-click', row, prop, ev)}
                        placeholder={`Please input ${prop}`}
                        v-model={row[prop]}
                        clearable
                      />
                    )
                  ) : Array.isArray(row[prop]) ? (
                    row[prop].join(';')
                  ) : (
                    row[prop]
                  )}
                </>
              )
            }}
          </wui-table-column>
        </>
      )
    }
    return () => (
      <wui-table
        data={model.value.list}
        height='100%'
        border
        class={e('table')}
        onRowContextmenu={handleRowMenu}>
        {{
          default: () => (
            <>
              <wui-table-column label='No.' type='index' width='80px' align='center' />
              <RenderTableColumn prop='name' label='Name' rules={calcEqRules.name} />
              <RenderTableColumn prop='value' label='Equation' />
              <RenderTableColumn prop='units' label='Units' />
              <RenderTableColumn prop='comments' label='Comments' />
              <wui-table-column label='Op' width='100px' align='center'>
                {{
                  default: ({ row, $index }: any) => (
                    <TableTool.Op flag={row.flag} onOp={op => handleOp(op, row, $index)} />
                  )
                }}
              </wui-table-column>
            </>
          ),
          empty: () => <TableTool.Empty onCustom-contextmenu={handleTableAreaCtxMenu} />
        }}
      </wui-table>
    )
  }
})
