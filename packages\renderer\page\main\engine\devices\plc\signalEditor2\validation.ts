import { WuiMessage } from '@wuk/wui'

export interface ValidationOptions {
  min: number
  max: number
  offset?: number
}

export interface ValidationResult {
  isValid: boolean
  message?: string
}

export class ValidationUtils {
  private static showMessage(message: string, type: 'warning' | 'error', offset = 90): void {
    WuiMessage({
      message,
      type,
      offset
    })
  }

  static validateNumber(value: any, fieldName: string, type: 'warning' | 'error' = 'warning'): ValidationResult {
    const numValue = Number(value)
    
    if (value !== '' && isNaN(numValue)) {
      const message = `${fieldName} must be a valid number`
      this.showMessage(message, type)
      return { isValid: false, message }
    }
    
    return { isValid: true }
  }

  static validateRange(min: number, max: number, type: 'warning' | 'error' = 'error'): ValidationResult {
    if (isNaN(min) || isNaN(max)) {
      const message = 'Min and Max values must be valid numbers'
      this.showMessage(message, type)
      return { isValid: false, message }
    }

    if (min >= max) {
      const message = 'Minimum value must be less than maximum value'
      this.showMessage(message, type)
      return { isValid: false, message }
    }

    return { isValid: true }
  }

  static validateValueInRange(
    value: any, 
    options: ValidationOptions, 
    fieldName: string, 
    type: 'warning' | 'error' = 'warning'
  ): ValidationResult {
    const numValue = Number(value)
    
    // First check if it's a valid number
    const numberValidation = this.validateNumber(value, fieldName, type)
    if (!numberValidation.isValid) {
      return numberValidation
    }

    // Then check if it's in range (only if value is not empty)
    if (value !== '' && (numValue < options.min || numValue > options.max)) {
      const message = `${fieldName} (${numValue}) must be between ${options.min} and ${options.max}`
      this.showMessage(message, type, options.offset)
      return { isValid: false, message }
    }

    return { isValid: true }
  }

  static validateTableRow(
    row: { x: any; y: any }, 
    rowIndex: number, 
    options: ValidationOptions, 
    type: 'warning' | 'error' = 'warning'
  ): ValidationResult {
    const xValidation = this.validateValueInRange(
      row.x, 
      options, 
      `Row ${rowIndex + 1} X value`, 
      type
    )
    
    if (!xValidation.isValid) {
      return xValidation
    }

    const yValidation = this.validateValueInRange(
      row.y, 
      options, 
      `Row ${rowIndex + 1} Y value`, 
      type
    )
    
    return yValidation
  }

  static validateTableData(
    data: Array<{ x: any; y: any }>, 
    options: ValidationOptions, 
    type: 'warning' | 'error' = 'error'
  ): ValidationResult {
    if (data.length === 0) {
      const message = 'Please add at least one row of data'
      this.showMessage(message, type, options.offset)
      return { isValid: false, message }
    }

    for (let i = 0; i < data.length; i++) {
      const rowValidation = this.validateTableRow(data[i], i, options, type)
      if (!rowValidation.isValid) {
        return rowValidation
      }
    }

    return { isValid: true }
  }

  static validateCoefficients(
    coeffs: string[], 
    degree: number, 
    options: ValidationOptions, 
    type: 'warning' | 'error' = 'error'
  ): ValidationResult {
    for (let i = 0; i <= degree; i++) {
      const validation = this.validateValueInRange(
        coeffs[i], 
        options, 
        `Coefficient at position ${i}`, 
        type
      )
      
      if (!validation.isValid) {
        return validation
      }
    }

    return { isValid: true }
  }

  static validateSingleCoefficient(
    value: string, 
    index: number, 
    options: ValidationOptions, 
    defaultValue: string,
    type: 'warning' | 'error' = 'warning'
  ): { isValid: boolean; shouldReset: boolean; resetValue?: string } {
    const numValue = Number(value)
    
    if (value !== '' && isNaN(numValue)) {
      this.showMessage('Coefficient must be a valid number', type, options.offset)
      return { isValid: false, shouldReset: true, resetValue: defaultValue }
    }

    if (value !== '' && (numValue < options.min || numValue > options.max)) {
      this.showMessage(
        `Coefficient value (${numValue}) must be between ${options.min} and ${options.max}`, 
        type, 
        options.offset
      )
      return { isValid: false, shouldReset: false }
    }

    return { isValid: true, shouldReset: false }
  }
}
