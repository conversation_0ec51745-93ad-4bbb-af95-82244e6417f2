import { computed } from 'vue'
import { useBizEngine, useBizMain, useHandler } from '@/renderer/hooks'
import { CalcMode, calcModefnMap, CalcModeType } from '../constants'
import { BizEngine, BizMain } from '@/renderer/logic'
import { CalcGroupable, CalsGroupItem, CfgFileType } from '@wuk/cfg'

/**
 * @description 决定接口调用的模块 main | engine
 */
export const useBiz = (mode: CalcModeType) => {
  const bizEngine = useBizEngine()
  // const bizMain = useBizMain()
  // todo:::
  const biz = computed(() => (mode === CalcMode.Sys_common ? bizEngine.value : bizEngine.value))
  const fnPtr = computed(() => calcModefnMap[mode])
  const bindHandler = (handler: (...args: any[]) => void) => {
    const BizClass = mode === CalcMode.Sys_common ? BizEngine : BizEngine
    useHandler(biz, BizClass[fnPtr.value.handleEventName], handler)
  }
  const api = {
    readCalcs: biz.value?.[fnPtr.value.readFnName].bind(biz.value), // group 读取计算
    modifyCalcs: biz.value?.[fnPtr.value.modifyFnName].bind(biz.value), // group 修改计算
    removeCalcs: biz.value?.[fnPtr.value.removeFnName].bind(biz.value), // group 删除计算
    addCalcs: biz.value?.[fnPtr.value.addFnName].bind(biz.value), // group 新增计算
    loadCalc: biz.value?.[fnPtr.value.loadCalcName].bind(biz.value), // group item加载计算
    modifyCalc: biz.value?.[fnPtr.value.modifyCalcName].bind(biz.value), // group item修改计算
    saveCalc: biz.value?.[fnPtr.value.saveCalcName].bind(biz.value), // group item保存计算
    bindHandler
  }
  const dragCalcApi = {
    baseFn: async (
      groupItem: CalsGroupItem,
      groupable: Partial<CalcGroupable>,
      type: CfgFileType,
      index: number
    ) => {
      try {
        const newGroupItem = {
          ...groupItem,
          file: groupItem.file.replace('../common/', ''),
          type
        }
        // 1、创建拖拽目标:::一层文件
        const res1 = await api.addCalcs?.(newGroupItem, index)

        // 2、拖拽目标:::插入二层文件内容
        const calcFile =
          newGroupItem.type === CfgFileType.Common
            ? `../common/${newGroupItem.file}`
            : newGroupItem.file
        const res2 = await api.modifyCalc?.(calcFile, groupable, newGroupItem.type)
        // 3、拖拽目标:::save 保存二层文件内容
        const res3 = await api.saveCalc?.(calcFile, newGroupItem.type)
        return res1 && res2 && res3
      } catch (err) {
        console.error('执行中断，错误原因：', err)
        return false
      }
    },
    copyCommonToSpecific: async (
      groupItem: CalsGroupItem,
      dropIndex: number,
      draggingIndex: number
    ) => {
      // 2.0、读取被拖拽:::二层内容
      const groupable = (await api.loadCalc?.(groupItem.file, groupItem.type)) || {}
      const res = await dragCalcApi.baseFn(
        groupItem,
        groupable,
        CfgFileType.EngineSpecific,
        dropIndex
      )
      const res1 = await api.modifyCalcs?.(draggingIndex, {
        ...groupItem,
        file: groupItem.file.replace('../common/', ''),
        type: CfgFileType.EngineSpecific
      })
      return res && res1
    },
    pasteSpecificToCommon: async (
      groupItem: CalsGroupItem,
      dropIndex: number,
      draggingIndex: number
    ) => {
      try {
        // 2、被拖拽:::remove 一层文件被拖拽移除老的内容
        const res2 = await api.removeCalcs?.(draggingIndex)
        // 2.0、读取被拖拽:::二层内容
        const groupable = (await api.loadCalc?.(groupItem.file, groupItem.type)) || {}
        const res1 = await dragCalcApi.baseFn(groupItem, groupable, CfgFileType.Common, dropIndex)
        return res1 && res2
      } catch (err) {
        console.error('执行中断，错误原因：', err)
        return false
      }
    }
  }
  return {
    ...api,
    ...dragCalcApi
  }
}
