import { BaseTableRow } from '@/renderer/components/TableTool'
import { PlcSignal, PlcSignals, PlcCfgOption } from '@wuk/cfg'
import { InjectionKey, Ref } from 'vue'

export interface SignalParam {
  currentPlcIndex: number
  plcDeviceIndex: number
}

export type PlcSignalTableRow = BaseTableRow<PlcSignal & { meta: PlcSignal }>

export interface SubmitSignalParams {
  currentPlcIndex: number
  plcDeviceIndex: number
  signalIndex?: number
  signalData?: any
  signalModel?: any
}

export interface PLCContext {
  plcList: Ref<PlcCfgOption[]>
  modifyPLCSetup: (plcSetupIndex: number, modifiedData: any) => Promise<boolean>
  submitSignalData: (params: SubmitSignalParams) => Promise<boolean>
}

export const plcContextKey: InjectionKey<PLCContext> = Symbol('plcsetpContext')
