<template>
  <div :class="styles.box">
    <div :class="styles.box_table">
      <wui-table
        border
        :data="calculateList"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
        @row-contextmenu="onRightClick"
      >
        <wui-table-column label="Group Name" min-width="200px" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <wui-input
              v-if="row.flag"
              v-model="row.group_name"
              clearable
              placeholder="Please input"
              style="width: 100%; margin-left: 0; height: 32px"
            />
            <span v-else style="cursor: pointer" @click="openModel(row)">{{ row.group_name }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default="{ row, $index }">
            <wui-icon v-if="!row.flag" @click="onEdit(row)">
              <EditPen />
            </wui-icon>
            <div v-else :class="styles.box_table_btn">
              <wui-icon @click="onConfirm(row, $index)">
                <Select />
              </wui-icon>
              <wui-popconfirm
                title="Are you sure you want to cancel this?"
                @confirm="onClose(row, $index)"
                width="295px"
              >
                <template #reference>
                  <wui-icon>
                    <CloseBold />
                  </wui-icon>
                </template>
              </wui-popconfirm>
            </div>
          </template>
        </wui-table-column>
        <template #empty>
          <div :class="styles.box_table_empty" @contextmenu.prevent="addTableColumn">
            <p>No Data</p>
          </div>
        </template>
      </wui-table>
    </div>

    <Viscosity v-if="viscosityShow" v-model:modelShow="viscosityShow" :params="viscosityParam" />
  </div>

  <!-- <MyDialog
    v-model="bModelShow"
    title="Table Group: viscosity"
    width="600px"
    @ok="openModel(2, 'submit')"
  >
    <div class="tabs">
      <wui-button>Operators</wui-button>
      <wui-button style="margin-left: 6px">Functions</wui-button>
      <wui-button style="margin-left: 6px">Parameters</wui-button>
    </div>
    <div :class="styles.tableBg">
      <wui-table
        :data="tableData8"
        border
        height="200px"
        style="width: 100%"
        :header-cell-style="{ background: '#e8e8e8', color: '#000000' }"
      >
        <wui-table-column prop="name" label="Name" align="center" show-overflow-tooltip />
        <wui-table-column prop="equation" label="Equation" align="center" show-overflow-tooltip />
        <wui-table-column label="Op" fixed="right" width="60px" align="center">
          <template #default>
            <wui-icon @click="openModel(3, 'tableIcon')">
              <EditPen />
            </wui-icon>
          </template>
        </wui-table-column>
      </wui-table>
    </div>
  </MyDialog>

  <MyDialog
    v-model="cModelShow"
    title="Table Group: fuelflow Table:KFactor2_T"
    width="760px"
    @ok="openModel(3, 'submit')"
  >
    <div :class="styles.form">
      <div :class="styles.form_box">
        <div :class="styles.form_box_content">
          <span>Expression: </span>
          <span>{{ 'TRUE' }}</span>
        </div>
        <div style="margin-left: 30px">
          <span>Review Date: </span>
          <span>{{ '17 Nov 2021' }}</span>
        </div>
      </div>
      <div :class="styles.form_box">
        <div :class="styles.form_box_content">
          <span>Manual Date: </span>
          <span>{{ '18 Dec 2024' }}</span>
        </div>
        <div :class="styles.form_box_content" style="margin-left: 30px; width: calc(100% - 220px)">
          <span style="width: 200px">Manual Revision: </span>
          <wui-input
            v-model="input"
            placeholder="Please input"
            style="width: 100%; height: 25px; margin-left: 0"
          />
        </div>
      </div>
      <div style="margin-bottom: 10px">
        <div>Comments/Reference:</div>
        <wui-input
          v-model="input"
          placeholder="Please input"
          type="textarea"
          style="width: 100%; margin-left: 0"
          resize="none"
          :rows="2"
        />
      </div>
      <div :class="styles.form_box" style="justify-content: space-between">
        <div :class="[styles.form_box_content, styles.form_box_change]">
          <span>Table Type: </span>
          <wui-select v-model="selecValue7" placeholder="Select" style="width: 130px">
            <wui-option
              v-for="item in options14"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
        </div>
        <div :class="[styles.form_box_content, styles.form_box_change]">
          <span>Store To Database: </span>
          <wui-switch
            v-model="switchShow10"
            style="--wui-switch-on-color: #13ce66; --wui-switch-off-color: #e2e2e3"
          />
        </div>
        <div :class="[styles.form_box_content, styles.form_box_change]">
          <span>Dynamic Expression: </span>
          <wui-switch
            v-model="switchShow11"
            style="--wui-switch-on-color: #13ce66; --wui-switch-off-color: #e2e2e3"
          />
        </div>
      </div>
    </div>
    <div :class="styles.tableBg">
      <wui-table
        v-if="btnShow1"
        :data="tableData9"
        height="200px"
        border
        style="width: 100%"
        :header-cell-style="{ background: '#e8e8e8', color: '#000000' }"
      >
        <wui-table-column prop="output" label="Output" align="center" show-overflow-tooltip />
        <wui-table-column prop="xInput" label="X Input" align="center" show-overflow-tooltip />
        <wui-table-column prop="yInput" label="X Input" align="center" show-overflow-tooltip />>
      </wui-table>
      <wui-input
        v-if="btnShow2"
        v-model="input"
        placeholder="Please input"
        type="textarea"
        style="width: 100%; margin-left: 0"
        resize="none"
        :rows="9"
      />
      <div v-if="btnShow3" style="height: 200px; border: 1px solid #bbbbbb"></div>
    </div>
    <div :class="styles.modelBox">
      <div
        :class="styles.modelBox_btn"
        :style="{ background: btnShow1 ? '#ffffff' : '#cecece' }"
        @click="onChangeBtn(1)"
      >
        Column List
      </div>
      <div
        :class="styles.modelBox_btn"
        :style="{ background: btnShow2 ? '#ffffff' : '#cecece' }"
        @click="onChangeBtn(2)"
      >
        Text
      </div>
      <div
        :class="styles.modelBox_btn"
        :style="{ background: btnShow3 ? '#ffffff' : '#cecece' }"
        @click="onChangeBtn(3)"
      >
        View Table
      </div>
    </div>
  </MyDialog> -->
</template>

<script lang="ts" setup>
import { ref, onMounted, toRaw } from 'vue'
import styles from './index.module.scss'
import { EditPen, Select, CloseBold } from '@element-plus/icons-vue'
// import MyDialog from '@/renderer/components/dialog/index.vue'
import { useRightMenu, useHandler, useBizEngine } from '@/renderer/hooks'
import { BizEngine } from '@/renderer/logic'
import { TablesOptions, TableItem } from '@wuk/cfg'
import { WuiMessage } from '@wuk/wui'
import Viscosity from './viscosity/index.vue'

interface newTableItem extends TableItem {
  meta?: TableItem
  flag: boolean
  type: string
}

const calculatePtr = useBizEngine()
const createRow = (): newTableItem => ({
  group_name: '',
  equation: '',
  option: {
    text: ''
  },
  flag: true,
  type: 'addType'
})
const calculateList = ref<newTableItem[]>([])
const input = ref('')
const bModelShow = ref(false)
const cModelShow = ref(false)
const btnShow1 = ref(true)
const btnShow2 = ref(false)
const btnShow3 = ref(false)
const switchShow10 = ref(true)
const switchShow11 = ref(true)
const selecValue7 = ref('')
const options14 = [
  {
    label: 'Polynomial',
    value: '1'
  },
  {
    label: 'Constant',
    value: '2'
  },
  {
    label: '2D',
    value: '3'
  },
  {
    label: '3D',
    value: '4'
  }
]
const tableData8 = [
  {
    name: 'Visc_T',
    equation: '(FuelType-"AV-Gas")'
  },
  {
    name: 'Visc_T',
    equation: '(FuelType-"AV-Gas")'
  }
]
const tableData9 = [
  {
    output: 3.0,
    xInput: -10.0,
    yInput: -10.0
  },
  {
    output: 4.0,
    xInput: 0.0,
    yInput: 0.0
  },
  {
    output: 5.0,
    xInput: 25.0,
    yInput: 25.0
  },
  {
    output: 6.0,
    xInput: 32.0,
    yInput: 32.0
  },
  {
    output: 7.0,
    xInput: 38.0,
    yInput: 38.0
  }
]
const viscosityShow = ref(false)
const viscosityParam = ref({})
const openModel = (item: newTableItem) => {
  viscosityParam.value = item
  viscosityShow.value = true
}

const onChangeBtn = (i: number) => {
  if (i === 1) {
    btnShow1.value = true
    btnShow2.value = false
    btnShow3.value = false
  } else if (i === 2) {
    btnShow1.value = false
    btnShow2.value = true
    btnShow3.value = false
  } else {
    btnShow1.value = false
    btnShow2.value = false
    btnShow3.value = true
  }
}

const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 70
  })
}
// 新增事件
const onAdd = () => {
  calculateList.value.push(createRow())
}
// 编辑事件
const onEdit = (item: newTableItem) => {
  if (item.flag) return
  item.flag = true
}
// 当表格没有数据，右键点击表格的事件
const menuAdd = useRightMenu([{ key: 'addKey', label: 'add' }], () => {
  onAdd()
})
const addTableColumn = (event: MouseEvent) => {
  event.preventDefault()
  menuAdd.show(event.clientX, event.clientY)
}
// 右键菜单
const menuPtr = useRightMenu(
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'midifyKey', label: 'midify' },
    { key: 'deleteKey', label: 'delete' }
  ],
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        onAdd()
        break
      case 'insertKey':
        calculateList.value.splice(rowIndex + 1, 0, { ...createRow(), type: 'insertType' })
        break
      case 'midifyKey':
        onEdit(row)
        break
      case 'deleteKey':
        const removeResult = await calculatePtr.value?.removeTable(rowIndex)
        if (!removeResult) return
        tipsMessage()
        break
      default:
        break
    }
  }
)
// 右键点击表格事件
const onRightClick = (row: any, column: any, event: MouseEvent) => {
  event.preventDefault()
  let rowIndex = -1
  rowIndex = calculateList.value.indexOf(row)
  menuPtr.show(event.clientX, event.clientY, { row, rowIndex })
}
// 关闭事件
const onClose = (item: newTableItem, index: number) => {
  const { type, meta = {} } = item
  const { group_name } = meta as TableItem
  if (type === 'addType' || type === 'insertType') {
    calculateList.value.splice(index, 1)
  } else {
    item.group_name = group_name
    item.flag = false
  }
}
// 提交事件
const onConfirm = async (item: newTableItem, index: number) => {
  const { group_name, equation, option, type } = item
  if (!group_name) {
    WuiMessage({
      message: 'Data cannot be empty',
      type: 'warning',
      offset: 70
    })
    return
  }
  const data = { group_name, equation, option: toRaw(option) }
  let editResult
  if (type === 'addType' || type === 'insertType') {
    editResult = await calculatePtr.value?.addTable(data, type === 'insertType' ? index : undefined)
  } else {
    editResult = await calculatePtr.value?.modifyTable(index, data)
  }
  if (!editResult) return
  item.flag = false
  tipsMessage()
}

// 数据处理
const getDataInfo = async () => {
  const { list = [] } = (await calculatePtr.value?.readTablesOptions()) || ({} as TablesOptions)
  // calculateList.value = list.map(item => {
  //   const meta = item
  //   const flag = false
  //   const type = ''
  //   return { ...item, meta, flag, type }
  // })
  calculateList.value = [
    {
      group_name: 'groupName1',
      equation: '',
      option: {
        text: ''
      },
      meta: {
        group_name: 'groupName1',
        equation: '',
        option: {
          text: ''
        }
      },
      flag: false,
      type: ''
    },
    {
      group_name: 'groupName2',
      equation: '',
      option: {
        text: ''
      },
      meta: {
        group_name: 'groupName2',
        equation: '',
        option: {
          text: ''
        }
      },
      flag: false,
      type: ''
    }
  ]
}

useHandler(calculatePtr, BizEngine.onTablesOptionsChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
})
</script>

<style lang="scss" stylemodule>
.wui-switch {
  margin-left: 15px;
}
.wui-select {
  margin-left: 15px;
}
.wui-icon {
  cursor: pointer;
  &:hover {
    color: #0c65ff;
  }
}

.wui-table {
  height: 100%;
  .wui-table__empty-text {
    height: 100%;
    position: relative;
  }
}
.wui-scrollbar__view {
  height: 100%;
}

.wui-popconfirm__action {
  display: flex;
  justify-content: center;
  .wui-button:nth-child(1) {
    display: block !important;
    background-color: #f0f0f0 !important;
    border: 1px solid #b6bece !important;
    color: #3d3d3d !important;
    &:hover {
      background-color: #909399 !important;
      color: #ffffff !important;
    }
  }
  .wui-button:nth-child(2) {
    background-color: #6282c1 !important;
    border: 1px solid #3c5992 !important;
    &:hover {
      background-color: rgba(98, 130, 193, 0.7) !important;
    }
  }
}
</style>
