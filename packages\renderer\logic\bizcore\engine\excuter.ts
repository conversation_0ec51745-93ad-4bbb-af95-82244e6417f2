import { RootCore, TargetClass } from '@/renderer/boots'
import {
  AttributeItem,
  CalsGroupItem,
  DashNumberItem,
  DisplayItem,
  DisplayQuadOption,
  EngineOptions,
  IAttribute,
  IDevice,
  IDisplay,
  IEngine,
  ITable,
  ITimer,
  SetupOptions,
  TimerItem,
  UDisplay,
  VibBandFilterOption,
  VibInputOption,
  VibSignalOption,
  VibSpectrumOption,
  VibSystemOption,
  VibTachInputOption,
  VibTFInputOption,
  ICalc,
  TableGroup,
  TableLib,
  TableCfg,
  TableData,
  CalcGroupable,
  LimitOptions,
  AlarmRelay,
  LimitCounter,
  LimitParameter,
  ILimit,
  PlcCfgOption,
  CfgFileType,
  ICalibrate
} from '@wuk/cfg'
import { BizEngine } from './types'

@TargetClass(BizEngine, RootCore, true)
export default class EngineImpl extends BizEngine {
  private _current = ''

  constructor(key: string) {
    super(key)

    this.handleEngineOptionsChanged = this.handleEngineOptionsChanged.bind(this)
    this.handleDashOptionsChanged = this.handleDashOptionsChanged.bind(this)
    this.handleDisplayOptionsChanged = this.handleDisplayOptionsChanged.bind(this)
    this.handleVibrationSignalsChanged = this.handleVibrationSignalsChanged.bind(this)
    this.handleVibrationSystemSetupChanged = this.handleVibrationSystemSetupChanged.bind(this)
    this.handleVibrationSetupChanged = this.handleVibrationSetupChanged.bind(this)
    this.handleVibrationInputSetupChanged = this.handleVibrationInputSetupChanged.bind(this)
    this.handleBroadbandFilterSetupChanged = this.handleBroadbandFilterSetupChanged.bind(this)
    this.handleTachometerInputSetupChanged = this.handleTachometerInputSetupChanged.bind(this)
    this.handleSpectrumAnalysisSetupChanged = this.handleSpectrumAnalysisSetupChanged.bind(this)
    this.handleTrackingFilterSetupChanged = this.handleTrackingFilterSetupChanged.bind(this)
    this.handleCalcsInitialOChanged = this.handleCalcsInitialOChanged.bind(this)
    this.handleCalcsFinalChanged = this.handleCalcsFinalChanged.bind(this)
    this.handleCalcsSignalChanged = this.handleCalcsSignalChanged.bind(this)
    this.handleTablesOptionsChanged = this.handleTablesOptionsChanged.bind(this)
    this.handleTimersOptionsChanged = this.handleTimersOptionsChanged.bind(this)
    this.handleLCSetupOptionsChanged = this.handleLCSetupOptionsChanged.bind(this)
    this.handleDisplayFileChanged = this.handleDisplayFileChanged.bind(this)
    this.handleAttributeOptionsChanged = this.handleAttributeOptionsChanged.bind(this)
    this.handleCalcFileChanged = this.handleCalcFileChanged.bind(this)
    this.handleTableCfgChanged = this.handleTableCfgChanged.bind(this)
    this.handleCalcsOptionsChanged = this.handleCalcsOptionsChanged.bind(this)
    this.handleLimitOptionsChanged = this.handleLimitOptionsChanged.bind(this)
  }

  get current(): string {
    return this._current
  }

  async init() {
    await super.init()

    this.app.sdk?.eng.on(IEngine.OnEngineOptions, this.handleEngineOptionsChanged)
    this.app.sdk?.eng.on(IEngine.OnDashOptions, this.handleDashOptionsChanged)
    this.app.sdk?.eng.on(IEngine.OnDisplayCrtOptions, this.handleDisplayOptionsChanged)
    this.app.sdk?.eng.device.on(IDevice.OnSetupOptions, this.handleVibrationSetupChanged)
    this.app.sdk?.eng.device.on(IDevice.OnVibSignalOption, this.handleVibrationSignalsChanged)
    this.app.sdk?.eng.device.on(IDevice.OnVibSystemOption, this.handleVibrationSystemSetupChanged)
    this.app.sdk?.eng.device.on(IDevice.OnVibInputOption, this.handleVibrationInputSetupChanged)
    this.app.sdk?.eng.device.on(
      IDevice.OnVibBandFilterOption,
      this.handleBroadbandFilterSetupChanged
    )
    this.app.sdk?.eng.device.on(
      IDevice.OnVibTachInputOption,
      this.handleTachometerInputSetupChanged
    )
    this.app.sdk?.eng.device.on(
      IDevice.OnVibSpectrumOption,
      this.handleSpectrumAnalysisSetupChanged
    )
    this.app.sdk?.eng.device.on(IDevice.OnVibTFInputOption, this.handleTrackingFilterSetupChanged)
    this.app.sdk?.eng.on(IEngine.OnCalcsInitOptions, this.handleCalcsInitialOChanged)
    this.app.sdk?.eng.on(IEngine.OnCalcsFinalOptions, this.handleCalcsFinalChanged)
    this.app.sdk?.eng.table.on(ITable.OnTablesOptions, this.handleTablesOptionsChanged)
    this.app.sdk?.eng.table.on(ITable.OnTableCfg, this.handleTableCfgChanged)
    this.app.sdk?.eng.timer.on(ITimer.OnOptions, this.handleTimersOptionsChanged)
    this.app.sdk?.eng.device.on(IDevice.OnPLCSetupOption, this.handleLCSetupOptionsChanged)
    this.app.sdk?.eng.display.on(IDisplay.OnOptions, this.handleDisplayFileChanged)
    this.app.sdk?.eng.attribute.on(IAttribute.OnOptions, this.handleAttributeOptionsChanged)

    this.app.sdk?.eng.calc.on(ICalc.OnCalc, this.handleCalcFileChanged)
    this.app.sdk?.eng.calc.on(ICalc.OnOptions, this.handleCalcsOptionsChanged)
    this.app.sdk?.eng.limit.on(ILimit.OnLimitOptions, this.handleLimitOptionsChanged)

    this.app.sdk?.calibrate.on(ICalibrate.OnVirSignals, this.handleCalcsSignalChanged)
  }

  /**
   * Engine Options
   */
  async readEngineOptions() {
    const result = await this.app.sdk?.eng.readEngineOptions()
    return result
  }
  async writeEngineOptions(val: Partial<EngineOptions>) {
    const result = await this.app.sdk?.eng.writeEngineOptions(val)
    return !!result
  }

  /**
   * Valid Engine Dash Numbers
   */
  async readDashOptions() {
    const result = await this.app.sdk?.eng.readDashOptions()
    return result
  }
  async removeDash(index: number) {
    const result = await this.app.sdk?.eng.removeDash(index)
    return !!result
  }
  async addDash(val: DashNumberItem, index?: number) {
    const result = await this.app.sdk?.eng.addDash(val, index)
    return !!result
  }
  async modifyDash(index: number, val: Partial<DashNumberItem>) {
    const result = await this.app.sdk?.eng.modifyDash(index, val)
    return !!result
  }

  /**
   * Display list
   */
  async readDisplayOptions() {
    const result = await this.app.sdk?.eng.readDisCrtOptions()
    return result
  }

  async removeDisplayQuad(index: number, quadIdx?: number) {
    const result = await this.app.sdk?.eng.removeDisplayQuad(index, quadIdx)
    return !!result
  }

  async addDisplayCrt(name: string, index?: number) {
    const result = await this.app.sdk?.eng.addDisplayCrt(name, index)
    return !!result
  }

  async addDisplayQuad(val: DisplayQuadOption, index: number, quadIdx?: number) {
    const result = await this.app.sdk?.eng.addDisplayQuad(val, index, quadIdx)
    return !!result
  }

  async modifyDisplayCrt(index: number, name: string) {
    const result = await this.app.sdk?.eng.modifyDisplayCrt(index, name)
    return !!result
  }

  async modifyDisplayQuad(index: number, val: Partial<DisplayQuadOption>, quadIdx: number) {
    const result = await this.app.sdk?.eng.modifyDisplayQuad(index, val, quadIdx)
    return !!result
  }
  /**
   * Vibration Signals
   */
  async readVibSignals() {
    return this.app.sdk?.eng.device.readVibSignals() || []
  }

  async removeVibSignal(index: number) {
    const result = await this.app.sdk?.eng.device.removeVibSignal(index)
    return !!result
  }

  async addVibSignal(val: VibSignalOption, index?: number) {
    const result = await this.app.sdk?.eng.device.addVibSignal(val, index)
    return !!result
  }

  async modifyVibSignal(index: number, val: Partial<VibSignalOption>) {
    const result = await this.app.sdk?.eng.device.modifyVibSignal(index, val)
    return !!result
  }

  // Vibration System Setup

  async readSetups() {
    return this.app.sdk?.eng.device.readSetups() || []
  }

  async removeSetup(index: number) {
    const result = await this.app.sdk?.eng.device.removeSetup(index)
    return !!result
  }

  async addSetup(val: SetupOptions, index?: number) {
    const result = await this.app.sdk?.eng.device.addSetup(val, index)
    return !!result
  }

  async modifySetup(index: number, val: Partial<SetupOptions>) {
    const result = await this.app.sdk?.eng.device.modifySetup(index, val)
    return !!result
  }

  async readVibSystem(index: number) {
    return this.app.sdk?.eng.device.readVibSystem(index)
  }

  async writeVibSystem(index: number, val: Partial<VibSystemOption>) {
    const result = await this.app.sdk?.eng.device.writeVibSystem(index, val)
    return !!result
  }

  // Vibration Input Setup
  async readVibInput(index: number) {
    return this.app.sdk?.eng.device.readVibInput(index)
  }

  async writeVibInput(index: number, idx: number, val: Partial<VibInputOption>) {
    const result = await this.app.sdk?.eng.device.writeVibInput(index, idx, val)
    return !!result
  }

  // Broadband Filter Setup
  async readVibBandFilter(index: number) {
    return this.app.sdk?.eng.device.readVibBandFilter(index)
  }

  async writeVibBandFilter(index: number, idx: number, val: Partial<VibBandFilterOption>) {
    const result = await this.app.sdk?.eng.device.writeVibBandFilter(index, idx, val)
    return !!result
  }

  // Tachometer Input Setup
  async readVibTachInput(index: number) {
    return this.app.sdk?.eng.device.readVibTachInput(index)
  }

  async writeVibTachInput(index: number, idx: number, val: Partial<VibTachInputOption>) {
    const result = await this.app.sdk?.eng.device.writeVibTachInput(index, idx, val)
    return !!result
  }

  // Spectrum Analysis Setup
  async readVibSpectrum(index: number) {
    return this.app.sdk?.eng.device.readVibSpectrum(index)
  }

  async writeVibSpectrum(index: number, idx: number, val: Partial<VibSpectrumOption>) {
    const result = await this.app.sdk?.eng.device.writeVibSpectrum(index, idx, val)
    return !!result
  }

  // Tracking Filter Setup
  async readVibTFInput(index: number) {
    return this.app.sdk?.eng.device.readVibTFInput(index)
  }

  async writeVibTFInput(index: number, idx: number, val: Partial<VibTFInputOption>) {
    const result = await this.app.sdk?.eng.device.writeVibTFInput(index, idx, val)
    return !!result
  }

  async readAttributes() {
    const result = await this.app.sdk?.eng.attribute.readOptions()
    return result
  }

  async removeAttribute(index: number) {
    const result = await this.app.sdk?.eng.attribute.removeAttribute(index)
    return !!result
  }

  async addAttribute(val: AttributeItem, index?: number) {
    const result = await this.app.sdk?.eng.attribute.addAttribute(val, index)
    return !!result
  }

  async modifyAttribute(index: number, val: Partial<AttributeItem>) {
    const result = await this.app.sdk?.eng.attribute.modifyAttribute(index, val)
    return !!result
  }

  /**
   * PLC Setup
   */
  async readPLCSetups() {
    return this.app.sdk?.eng.device.readPLCSetups()
  }

  async modifyPLCSetup(index: number, val: Partial<PlcCfgOption>) {
    const result = await this.app.sdk?.eng.device.modifyPLCSetup(index, val)
    return !!result
  }

  /**
   * Calcs Initial
   */
  async readCalcsInitOptions() {
    return this.app.sdk?.eng.readCalcsInitOptions() || []
  }

  async removeCalcsInit(index: number) {
    const result = await this.app.sdk?.eng.removeCalcsInit(index)
    return !!result
  }

  async addCalcsInit(val: CalsGroupItem, index?: number) {
    const result = await this.app.sdk?.eng.addCalcsInit(val, index)
    return !!result
  }

  async modifyCalcsInit(index: number, val: Partial<CalsGroupItem>) {
    const result = await this.app.sdk?.eng.modifyCalcsInit(index, val)
    return !!result
  }

  /**
   * Calcs Final
   */
  async readCalcsFinalOptions() {
    return this.app.sdk?.eng.readCalcsFinalOptions() || []
  }

  async removeCalcsFinal(index: number) {
    const result = await this.app.sdk?.eng.removeCalcsFinal(index)
    return !!result
  }

  async addCalcsFinal(val: CalsGroupItem, index?: number) {
    const result = await this.app.sdk?.eng.addCalcsFinal(val, index)
    return !!result
  }

  async modifyCalcsFinal(index: number, val: Partial<CalsGroupItem>) {
    const result = await this.app.sdk?.eng.modifyCalcsFinal(index, val)
    return !!result
  }

  /**
   * Calcs Virtual Signal
   */
  async readCalcsSignalOptions() {
    return this.app.sdk?.calibrate.readVirSignals() || []
  }

  async removeCalcsSignal(index: number) {
    const result = await this.app.sdk?.calibrate.removeVirSignal(index)
    return !!result
  }

  async addCalcsSignal(val: CalsGroupItem, index?: number) {
    const result = await this.app.sdk?.calibrate.addVirSignal(val, index)
    return !!result
  }

  async modifyCalcsSignal(index: number, val: Partial<CalsGroupItem>) {
    const result = await this.app.sdk?.calibrate.modifyVirSignal(index, val)
    return !!result
  }

  /**
   * Tables
   */
  async readTableOptions() {
    return this.app.sdk?.eng.table.readOptions()
  }

  async removeTableGroup(index: number) {
    const result = await this.app.sdk?.eng.table.removeGroup(index)
    return !!result
  }
  async addTableGroup(val: TableGroup, index?: number): Promise<boolean> {
    const result = await this.app.sdk?.eng.table.addGroup(val, index)
    return !!result
  }
  async modifyTableGroup(index: number, val: Partial<TableGroup>): Promise<boolean> {
    const result = await this.app.sdk?.eng.table.modifyGroup(index, val)
    return !!result
  }

  async removeTable(groupName: string, index: number): Promise<boolean> {
    const result = await this.app.sdk?.eng.table.removeTable(groupName, index)
    return !!result
  }
  async addTable(groupName: string, val: TableData, index?: number): Promise<boolean> {
    const result = await this.app.sdk?.eng.table.addTable(groupName, val, index)
    return !!result
  }
  async modifyTable(groupName: string, index: number, val: Partial<TableData>): Promise<boolean> {
    const result = await this.app.sdk?.eng.table.modifyTable(groupName, index, val)
    return !!result
  }

  async readTableCfg(groupName: string): Promise<TableCfg | undefined> {
    return this.app.sdk?.eng.table.readTableCfg(groupName)
  }
  async modifyTableCfg(groupName: string, val: Partial<TableCfg>): Promise<boolean> {
    const result = await this.app.sdk?.eng.table.modifyTableCfg(groupName, val)
    return !!result
  }

  async removeTableLib(groupName: string, tableName: string): Promise<boolean> {
    const result = await this.app.sdk?.eng.table.removeTableLib(groupName, tableName)
    return !!result
  }
  async addTableLib(groupName: string, tableName: string, val: TableLib): Promise<boolean> {
    const result = await this.app.sdk?.eng.table.addTableLib(groupName, tableName, val)
    return !!result
  }
  async modifyTableLib(
    groupName: string,
    tableName: string,
    val: Partial<TableLib>
  ): Promise<boolean> {
    const result = await this.app.sdk?.eng.table.modifyTableLib(groupName, tableName, val)
    return !!result
  }

  /**
   * System Timers
   */
  async readTimersOptions() {
    const result = await this.app.sdk?.eng.timer.readOptions()
    return result
  }

  async removeTimer(index: number) {
    const result = await this.app.sdk?.eng.timer.removeTimer(index)
    return !!result
  }

  async addTimer(val: TimerItem, index?: number) {
    const result = await this.app.sdk?.eng.timer.addTimer(val, index)
    return !!result
  }

  async modifyTimer(index: number, val: Partial<TimerItem>) {
    const result = await this.app.sdk?.eng.timer.modifyTimer(index, val)
    return !!result
  }

  async readDisplayDefinedOptions() {
    const result = await this.app.sdk?.eng.display.readOptions()
    return result
  }
  async removeDisplayFile(index: number) {
    const result = await this.app.sdk?.eng.display.removeFile(index)
    return !!result
  }
  async addDisplayFile(val: DisplayItem, index?: number) {
    const result = await this.app.sdk?.eng.display.addFile(val, index)
    return !!result
  }
  async modifyDisplayFile(index: number, val: Partial<DisplayItem>) {
    const result = await this.app.sdk?.eng.display.modifyFile(index, val)
    return !!result
  }

  async loadDisplay(index: number) {
    const result = await this.app.sdk?.eng.display.loadDisplay(index)
    return result
  }

  async modifyDisplay(index: number, val: Partial<UDisplay>) {
    const result = await this.app.sdk?.eng.display.modifyDisplay(index, val)
    return !!result
  }

  async saveDisplay(index: number) {
    const result = await this.app.sdk?.eng.display.saveDisplay(index)
    return !!result
  }

  async readCalcsOptions(type?: CfgFileType): Promise<Array<CalsGroupItem>> {
    const { files = [] } = (await this.app.sdk?.eng.calc.readOptions(type)) || {}
    return files.map(item => ({
      file: item.file,
      group_name: item.name,
      type: item.type
    }))
  }
  async removeCalcFile(index: number, type?: CfgFileType): Promise<boolean> {
    const result = await this.app.sdk?.eng.calc.removeFile(index, type)
    return !!result
  }
  async addCalcFile(item: CalsGroupItem, index?: number, type?: CfgFileType): Promise<boolean> {
    const result = await this.app.sdk?.eng.calc.addFile(
      {
        file: item.file,
        name: item.group_name,
        type: item.type,
        depends: []
      },
      index,
      type
    )
    return !!result
  }
  async modifyCalcFile(
    index: number,
    item: Partial<CalsGroupItem>,
    type?: CfgFileType
  ): Promise<boolean> {
    const result = await this.app.sdk?.eng.calc.modifyFile(
      index,
      {
        file: item.file,
        name: item.group_name,
        type: item.type,
        depends: []
      },
      type
    )
    return !!result
  }

  async loadCalc(file: string, type?: CfgFileType): Promise<CalcGroupable | undefined> {
    const result = await this.app.sdk?.eng.calc.loadCalc(file, type)
    return result
  }
  async modifyCalc(
    file: string,
    val: Partial<CalcGroupable>,
    type?: CfgFileType
  ): Promise<boolean> {
    const result = await this.app.sdk?.eng.calc.modifyCalc(file, val, type)
    return !!result
  }
  async saveCalc(file: string, type?: CfgFileType): Promise<boolean> {
    const result = await this.app.sdk?.eng.calc.saveCalc(file, type)
    return !!result
  }
  async loadCalcText(file: string, type?: CfgFileType): Promise<string | undefined> {
    const result = await this.app.sdk?.eng.calc.loadCalcText(file, type)
    return result
  }
  async saveCalcText(file: string, text: string, type?: CfgFileType): Promise<boolean> {
    const result = await this.app.sdk?.eng.calc.saveCalcText(file, text, type)
    return !!result
  }

  async readLimitOptions(): Promise<LimitOptions | undefined> {
    return this.app.sdk?.eng.limit.readOptions()
  }
  async modifyLimitOptions(val: Partial<LimitOptions>) {
    const result = await this.app.sdk?.eng.limit.modifyOptions(val)
    return !!result
  }
  async modifyAlarmRelay(val: Partial<AlarmRelay>): Promise<boolean> {
    const result = await this.app.sdk?.eng.limit.modifyAlarmRelay(val)
    return !!result
  }
  async removeLimitCounter(index: number): Promise<boolean> {
    const result = await this.app.sdk?.eng.limit.removeLimitCounter(index)
    return !!result
  }
  async addLimitCounter(val: LimitCounter, index?: number): Promise<boolean> {
    const result = await this.app.sdk?.eng.limit.addLimitCounter(val, index)
    return !!result
  }
  async modifyLimitCounter(index: number, val: Partial<LimitCounter>): Promise<boolean> {
    const result = await this.app.sdk?.eng.limit.modifyLimitCounter(index, val)
    return !!result
  }
  async removeLimitParameter(index: number): Promise<boolean> {
    const result = await this.app.sdk?.eng.limit.removeLimitParameter(index)
    return !!result
  }
  async addLimitParameter(val: LimitParameter, index?: number): Promise<boolean> {
    const result = await this.app.sdk?.eng.limit.addLimitParameter(val, index)
    return !!result
  }
  async modifyLimitParameter(index: number, val: Partial<LimitParameter>): Promise<boolean> {
    const result = await this.app.sdk?.eng.limit.modifyLimitParameter(index, val)
    return !!result
  }

  private handleEngineOptionsChanged() {
    this.emit(BizEngine.onEngineOptionsChanged)
  }

  private handleDashOptionsChanged() {
    this.emit(BizEngine.onDashOptionsChanged)
  }

  private handleDisplayOptionsChanged() {
    this.emit(BizEngine.onDisplayOptionsChanged)
  }

  private handleDisplayFileChanged(name: string) {
    this.emit(BizEngine.onDisplayFileChanged, name)
  }

  private handleVibrationSignalsChanged() {
    this.emit(BizEngine.onVibrationSignalsChanged)
  }

  private handleVibrationSetupChanged() {
    this.emit(BizEngine.onVibrationSetupChanged)
  }

  private handleVibrationSystemSetupChanged() {
    this.emit(BizEngine.onVibrationSystemSetupChanged)
  }

  private handleVibrationInputSetupChanged() {
    this.emit(BizEngine.onVibrationInputSetupChanged)
  }

  private handleBroadbandFilterSetupChanged() {
    this.emit(BizEngine.onBroadbandFilterSetupChanged)
  }

  private handleTachometerInputSetupChanged() {
    this.emit(BizEngine.onTachometerInputSetupChanged)
  }

  private handleSpectrumAnalysisSetupChanged() {
    this.emit(BizEngine.onSpectrumAnalysisSetupChanged)
  }

  private handleTrackingFilterSetupChanged() {
    this.emit(BizEngine.onTrackingFilterSetupChanged)
  }

  private handleCalcsInitialOChanged() {
    this.emit(BizEngine.onCalcsInitialOChanged)
  }

  private handleCalcsFinalChanged() {
    this.emit(BizEngine.onCalcsFinalChanged)
  }

  private handleCalcsSignalChanged() {
    this.emit(BizEngine.onCalcsSignalChanged)
  }

  private handleTablesOptionsChanged() {
    this.emit(BizEngine.onTablesOptionsChanged)
  }

  private handleTableCfgChanged(groupName: string) {
    this.emit(BizEngine.onTableCfgChanged, groupName)
  }

  private handleCalcsOptionsChanged() {
    this.emit(BizEngine.onCalcsOptionsChanged)
  }

  private handleLimitOptionsChanged() {
    this.emit(BizEngine.onLimitOptionsChanged)
  }

  private handleTimersOptionsChanged() {
    this.emit(BizEngine.onTimersOptionsChanged)
  }

  private handleLCSetupOptionsChanged() {
    this.emit(BizEngine.onPLCSetupOptionsChanged)
  }

  private handleAttributeOptionsChanged() {
    this.emit(BizEngine.onAttributeOptionsChanged)
  }

  private handleCalcFileChanged(file: string) {
    this.emit(BizEngine.onCalcFileChanged, file)
  }
}
