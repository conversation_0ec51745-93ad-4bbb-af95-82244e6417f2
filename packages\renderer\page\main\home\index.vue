<template>
  <main-layout :class="styles.home">
    <template #title> CONTRUCTOR </template>
    <template #header>
      <Customer :list="customers" :current="customer" @change="handleCustomerChanged" />
    </template>
    <template #footer>
      <div />
    </template>
    <div :class="styles.home_main">
      <tool-bar><main-tool @tool-click="handleToolClicked" /></tool-bar>
      <div :class="styles.home_view">
        <panel-group direction="vertical">
          <panel>
            <tab-view
              ref="tabview"
              v-model:search-keyword="searchKeyword"
              :class="styles.home_view_tab"
              :tabs="tabs"
              @change="handleTabViewChanged"
            >
              <router-view v-slot="{ Component }">
                <component :is="Component" />
              </router-view>
            </tab-view>
          </panel>
          <panel-resize-handle :class="styles.home_view_resize_handle" />
          <panel :default-size="25" :min-size="25">
            <message :class="styles.home_view_message">
              <template #default>
                <div ref="messageBox" :class="styles.home_view_message_box">
                  <span
                    v-for="(item, index) in messageData"
                    :key="index"
                    :class="styles.home_view_message_box_content"
                  >
                    {{ item }}
                  </span>
                </div>
              </template>
            </message>
          </panel>
        </panel-group>
      </div>
    </div>
  </main-layout>
  <Operation :type="mainToolType" @reset:type="mainToolType = ''" />
</template>

<script lang="ts">
export const homeContextKey = Symbol('homeContextKey')
export type HomeContext = {
  searchKeyword: Ref<string>
  changeSearchKeyword: (val: string) => void
}
</script>
<script setup lang="ts" name="Home">
import { computed, onMounted, ref, provide, Ref, watch, nextTick } from 'vue'
import { Panel, PanelGroup, PanelResizeHandle } from 'vue-resizable-panels'
import styles from './index.module.scss'
import { MainLayout, TabItemProps, TabView, TabviewInvokes, ToolBar } from '@/renderer/components'
import { useBizMain, useHandler } from '@/renderer/hooks'
import { BizMain, PageRouter } from '@/renderer/logic'
import Customer from './customer/index.vue'
import Message from './message'
import Operation from './operation/index.vue'
import MainTool, { MainToolType } from './tools'
import { useRouter } from 'vue-router'
import { UDate } from '@/renderer/utils'
const tabs = ref<TabItemProps[]>([])
const tabview = ref<TabviewInvokes>()
const mainPtr = useBizMain()
const router = useRouter()
const messageData = ref<string[]>([])
const searchKeyword = ref('')
const mainToolType = ref<MainToolType | ''>('')
const messageBox = ref<HTMLElement | null>(null)

provide<HomeContext>(homeContextKey, {
  searchKeyword,
  changeSearchKeyword: (val: string) => {
    searchKeyword.value = val
  }
})
const handleToolClicked = async (type: MainToolType) => {
  if (type === 'Setting') {
    tabview.value?.add({ title: 'Setting', key: 'main_setting' })
  }
  mainToolType.value = type
}
const handleTabViewChanged = (index: number, item?: TabItemProps) => {
  const name = (item?.key as string) || PageRouter.PR_MAIN_HOME
  router.replace({ name })
}
const handleCustomerChanged = (val: string) => {
  mainPtr.value?.changeCustomer(val)
}
const customer = ref('')
const customers = computed(() => {
  const list = mainPtr.value?.getCustomers() || []
  return list.map(val => ({ label: val.name, value: val.name }))
})

const updateCustomer = () => {
  customer.value = mainPtr.value?.customer?.name || ''
}
const updateMessages = async () => {
  const messages = (await mainPtr.value?.getMessages()) || []
  messageData.value = messages.map(
    val => `${new UDate(val.timestamp).format('yyyy-MM-dd hh:mm:ss')} ${val.message}`
  )
}

useHandler(mainPtr, BizMain.onCustomersChanged, updateCustomer)

useHandler(mainPtr, BizMain.onMessageArrived, updateMessages)

onMounted(async () => {
  updateCustomer()
  updateMessages()
})

watch(messageData, () => {
  nextTick(() => {
    const box = messageBox.value
    box && (box.scrollTop = box.scrollHeight)
  })
})
</script>
