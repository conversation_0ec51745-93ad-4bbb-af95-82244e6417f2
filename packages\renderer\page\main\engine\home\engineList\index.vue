<template>
  <div>
    <h2>Engine Options</h2>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Test Mode</h4>
        <wui-select
          v-model="engineData.test_mode"
          placeholder="Select"
          @change="onEngineChange('test_mode', $event)"
        >
          <wui-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Crs On Param</h4>
        <div :class="styles.box_menu_options_button" @click="openModel('crs_on_param')">
          {{ engineData.crs_on_param || 'None' }}
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Run Limits Param</h4>
        <div :class="styles.box_menu_options_button" @click="openModel('run_limits_param')">
          {{ engineData.run_limits_param || 'None' }}
        </div>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>PLA Idle Default (degs)</h4>
        <wui-input
          v-model="engineData.pla_idle_default"
          placeholder="Please input"
          @change="onEngineChange('pla_idle_default', $event)"
        />
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>PLA Takeoff Default (degs)</h4>
        <wui-input
          v-model="engineData.pla_takeoff_default"
          placeholder="Please input"
          @change="onEngineChange('pla_takeoff_default', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import styles from '../index.module.scss'
import { BizEngine } from '@/renderer/logic'
import { useBizEngine, useHandler } from '@/renderer/hooks'
import { WuiMessage } from '@wuk/wui'
import { EngineOptions } from '@wuk/cfg'
import { useParameterDialog } from '@/renderer/utils/common'

const homePtr = useBizEngine()
const engineData = ref<EngineOptions>({
  test_mode: 0,
  crs_on_param: '',
  run_limits_param: '',
  pla_idle_default: '',
  pla_takeoff_default: ''
})
const options = [
  {
    label: 'Test With Hardware',
    value: 0
  },
  {
    label: 'No Hardware',
    value: 1
  }
]
const onEngineChange = async (key: string, value: string | number) => {
  const result = await homePtr.value?.writeEngineOptions({ [key]: value })
  if (!result) return
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 70
  })
}
const openModel = async (key: string) => {
  const name = await useParameterDialog()
  onEngineChange(key, name)
}
const getDataInfo = async () => {
  engineData.value = (await homePtr.value?.readEngineOptions()) || ({} as EngineOptions)
}

useHandler(homePtr, BizEngine.onEngineOptionsChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
})
</script>
