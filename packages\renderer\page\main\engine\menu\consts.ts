import homeIcon from './image/home_icon.png'
import homeIconSel from './image/home_icon_sel.png'

import displaysIcon from './image/displays_icon.png'
import displaysIconSel from './image/displays_icon_sel.png'

import devicesIcon from './image/devices_icon.png'
import devicesIconSel from './image/devices_icon_sel.png'

import calculationsIcon from './image/calculations_icon.png'

export interface BizEngineItem {
  key: string
  label: string
  level: number
  router?: string
  icon?: (val: boolean) => string

  items?: BizEngineItem[]
}

export const kMenuItems: BizEngineItem[] = [
  {
    key: '1',
    label: 'Home',
    level: 1,
    router: 'engine_home',
    icon: val => (val && homeIconSel) || homeIcon
  },
  {
    key: '2',
    label: 'Displays',
    level: 1,
    icon: val => (val && displaysIconSel) || displaysIcon,
    items: [
      { key: '2-1', label: 'User Difined Displays', level: 2, router: 'engine_displays_edito' },
      { key: '2-2', label: 'Displays List', level: 2, router: 'engine_displays_list' },
      {
        key: '2-3',
        label: 'Parameter Descriptions',
        level: 2,
        router: 'engine_displays_descriptons'
      },
      { key: '2-4', label: 'Parameter Attributes', level: 2, router: 'engine_displays_attributes' }
    ]
  },
  {
    key: '3',
    label: 'Devices',
    level: 1,
    icon: val => (val && devicesIconSel) || devicesIcon,
    items: [
      {
        key: '3-1',
        label: 'vlbsys',
        level: 2,
        items: [
          {
            key: '3-1-1',
            level: 3,
            label: 'Vibsystem Configuration',
            router: 'engine_devices_configuration'
          },
          { key: '3-1-2', label: 'Vibration Signals', level: 3, router: 'engine_devices_signals' }
        ]
      },
      { key: '3-2', label: 'PLC Setup', level: 2, router: 'engine_devices_plc' }
    ]
  },
  {
    key: '4',
    label: 'Calculations',
    level: 1,
    icon: val => calculationsIcon,
    items: [
      {
        key: '4-1',
        label: 'Calcs',
        level: 2,
        items: [
          { key: '4-1-1', label: 'Initial', level: 3, router: 'engine_calculations_initial' },
          { key: '4-1-2', label: 'Final', level: 3, router: 'engine_calculations_final' },
          { key: '4-1-3', label: 'Virtual Signal', level: 3, router: 'engine_calculations_virtual' }
        ]
      },
      { key: '4-2', label: 'Tables', level: 2, router: 'engine_calculations_tables' },
      { key: '4-3', label: 'System Timers', level: 2, router: 'engine_calculations_timers' },
      {
        key: '4-4',
        label: 'Engine Declaraions',
        level: 2,
        router: 'engine_calculations_declaraions'
      },
      { key: '4-5', label: 'Define System Limits', level: 2, router: 'engine_calculations_limits' }
    ]
  }
]
