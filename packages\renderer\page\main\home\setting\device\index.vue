<template>
  <div>
    <h2>Device Options</h2>
    <wui-form
      ref="deviceFormRef"
      label-width="0"
      label-position="left"
      validate-box-style="fill"
      validate-ellipsis="2"
      hide-required-asterisk
      inline-message
      status-icon
      validate-box-gap="3"
      validate-placement="bottom"
      :model="deviceModel"
      :rules="deviceRules"
    >
      <h3>Device List</h3>
      <wui-table
        border
        :class="styles.grid"
        :data="deviceModel.deviceList"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
        @row-contextmenu="handleRightClick"
      >
        <wui-table-column label="Type" width="130px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.id"
              placeholder="Select"
              style="width: 100%; margin-left: 0"
            >
              <wui-option
                v-for="item in idOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(idOptions, row.id) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Name" width="120px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item
              v-if="row.flag"
              :prop="`deviceList.${$index}.name`"
              :rules="deviceRules.name"
            >
              <wui-input
                v-model="row.name"
                clearable
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
              />
            </wui-form-item>
            <span v-else>{{ row.name }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Interface" min-width="100px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.inter"
              placeholder="Select"
              style="width: 100%; margin-left: 0"
            >
              <wui-option
                v-for="item in options12"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(options12, row.inter) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Scan Rate" min-width="100px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item
              v-if="row.flag"
              :prop="`deviceList.${$index}.scan_rate`"
              :rules="deviceRules.scan_rate"
            >
              <wui-input-number
                v-model="row.scan_rate"
                clearable
                :min="1"
                :max="200"
                :step="1"
                :controls="false"
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
              />
            </wui-form-item>
            <span v-else>{{ row.scan_rate }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Test Mode" min-width="250px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.inst_addr"
              placeholder="Select"
              style="width: 100%; margin-left: 0"
            >
              <wui-option
                v-for="item in options13"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(options13, row.inst_addr) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default="{ row, $index }">
            <wui-icon v-if="!row.flag" @click="onEdit(row)">
              <EditPen />
            </wui-icon>
            <div v-else :class="styles.box_options_btn">
              <wui-icon @click="onConfirm(row, $index)">
                <Select />
              </wui-icon>
              <wui-popconfirm
                title="Are you sure you want to cancel this?"
                @confirm="onClose(row, $index)"
                width="295px"
              >
                <template #reference>
                  <wui-icon>
                    <CloseBold />
                  </wui-icon>
                </template>
              </wui-popconfirm>
            </div>
          </template>
        </wui-table-column>
        <template #empty>
          <div @contextmenu.prevent="addTableColumn">
            <p>No Data</p>
          </div>
        </template>
      </wui-table>
    </wui-form>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, shallowRef, triggerRef } from 'vue'
import styles from '../index.module.scss'
import { useRightMenu, useHandler, useBizMain } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { DeviceOptions, Hardware } from '@wuk/cfg'
import { WuiMessage, WuiForm } from '@wuk/wui'
import { convertType } from '@/renderer/utils/common'
import { EditPen, Select, CloseBold } from '@element-plus/icons-vue'
import { useDeviceRules } from './rule'
interface newHardware extends Hardware {
  meta?: Hardware
  flag: boolean
  type: string
}
const deviceFormRef = ref<InstanceType<typeof WuiForm>>()
const { deviceRules } = useDeviceRules()
const mainPtr = useBizMain()
const createRow = (): newHardware => ({
  id: '',
  name: '',
  inter: '',
  printer_type: '',
  scan_rate: 0,
  inst_addr: 0,
  flag: true,
  type: 'addType'
})
const deviceModel = reactive({
  deviceList: [] as newHardware[]
})

const idOptions = shallowRef<
  Array<{
    value: string
    label: string
  }>
>([])
const options12 = [
  {
    label: 'LAN',
    value: 'LAN'
  },
  {
    label: 'GPIB',
    value: 'GPIB'
  },
  {
    label: 'Serial',
    value: 'Serial'
  },
  {
    label: 'Parallel',
    value: 'Parallel'
  }
]
const options13 = [
  {
    label: 'Test With Device Hardware',
    value: 0
  },
  {
    label: 'No Device Hardware',
    value: 1
  }
]
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90
  })
}

// 新增事件
const onAdd = () => {
  deviceModel.deviceList.push(createRow())
}
// 编辑事件
const onEdit = (item: newHardware) => {
  if (item.flag) return
  item.flag = true
}
// 当表格没有数据，右键点击表格的事件
const menuAdd = useRightMenu([{ key: 'addKey', label: 'add' }], () => {
  onAdd()
})
const addTableColumn = (event: MouseEvent) => {
  event.preventDefault()
  menuAdd.show(event.clientX, event.clientY)
}
// 右键菜单
const menuPtr = useRightMenu(
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'midifyKey', label: 'midify' },
    { key: 'deleteKey', label: 'delete' }
  ],
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        onAdd()
        break
      case 'insertKey':
        deviceModel.deviceList.splice(rowIndex + 1, 0, { ...createRow(), type: 'insertType' })
        break
      case 'midifyKey':
        onEdit(row)
        break
      case 'deleteKey':
        const removeResult = await mainPtr.value?.removeDevice(rowIndex)
        if (!removeResult) return
        tipsMessage()
        break
      default:
        break
    }
  }
)
const handleRightClick = (row: any, column: any, event: MouseEvent) => {
  event.preventDefault()
  let rowIndex = -1
  rowIndex = deviceModel.deviceList.indexOf(row)
  menuPtr.show(event.clientX, event.clientY, { row, rowIndex })
}
// 关闭事件
const onClose = (item: newHardware, index: number) => {
  const { type, meta = {} } = item
  const { name, inter, id, scan_rate, inst_addr } = meta as Hardware
  if (type === 'addType' || type === 'insertType') {
    deviceModel.deviceList.splice(index, 1)
  } else {
    item.name = name
    item.inter = inter
    item.id = id
    item.scan_rate = scan_rate
    item.inst_addr = inst_addr
    item.flag = false
  }
}
// 提交事件
const onConfirm = async (item: newHardware, index: number) => {
  const { id, name, inter, printer_type, scan_rate, inst_addr, type } = item
  const valids = await deviceFormRef.value?.validateField([
    `deviceList.${index}.name`,
    `deviceList.${index}.scan_rate`
  ])
  if (!valids) return
  const data = { id, name, inter, printer_type, scan_rate, inst_addr }
  let editResult
  if (type === 'addType' || type === 'insertType') {
    editResult = await mainPtr.value?.addDevice(data, type === 'insertType' ? index : undefined)
  } else {
    editResult = await mainPtr.value?.modifyDevice(index, data)
  }
  if (!editResult) return
  item.flag = false
}

// 数据处理
const getDataInfo = async () => {
  const { list = [] } = (await mainPtr.value?.readDeviceOptions()) || ({} as DeviceOptions)
  console.log('list', list)
  deviceModel.deviceList = list.map(item => {
    const meta = item
    const flag = false
    const type = ''
    idOptions.value.push({
      value: item.id,
      label: item.id
    })
    return { ...item, meta, flag, type }
  })
  triggerRef(idOptions)
}

useHandler(mainPtr, BizMain.onDeviceOptionChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
})
</script>
