<template>
  <MyDialog v-model="isActive" title="PLC Digital Signal Dialog" @ok="onSubmit">
    <div :class="e('body')">
      <wui-form
        ref="signalFormRef"
        hide-required-asterisk
        :model="signalModel"
        label-width="120px"
        :class="e('form')"
      >
        <wui-form-item label="Source">
          <span>{{ signalModel.name }}</span>
        </wui-form-item>
        <wui-form-item label="Signal Range">
          <wui-input
            v-model="signalModel.signal_range.min"
            placeholder="Minimum"
            style="width: 100px"
          />
          <wui-input
            v-model="signalModel.signal_range.max"
            placeholder="Maximum"
            style="width: 100px; margin-left: 8px"
          />
          <wui-input
            v-model="signalModel.signal_range.unit"
            placeholder="Units"
            style="width: 100px; margin-left: 8px"
          />
        </wui-form-item>
        <wui-form-item label="Scale Range">
          <wui-input
            v-model="signalModel.scale_range.min"
            placeholder="Minimum"
            style="width: 100px"
          />
          <wui-input
            v-model="signalModel.scale_range.max"
            placeholder="Maximum"
            style="width: 100px; margin-left: 8px"
          />
          <wui-input
            v-model="signalModel.scale_range.unit"
            placeholder="Units"
            style="width: 100px; margin-left: 8px"
          />
        </wui-form-item>
        <wui-form-item label="Scale Factor">
          <wui-input
            v-model="signalModel.scale_factor"
            placeholder="Scale Factor"
            style="width: 240px"
          />
        </wui-form-item>
        <wui-form-item label="PLC Calculation">
          <wui-select v-model="signalModel.calib_data.type" style="width: 240px">
            <wui-option :value="0" label="None" />
            <wui-option :value="1" label="Polynomial" />
            <wui-option :value="2" label="Table" />
          </wui-select>
          <wui-button style="margin-top: 8px" @click="handleCalculation"
            >Edit PLC Calculations</wui-button
          >
        </wui-form-item>
        <wui-form-item label="Comments">
          <wui-input
            v-model="signalModel.comments_str"
            type="textarea"
            :rows="5"
            style="width: 100%; padding: 5px"
          />
        </wui-form-item>
      </wui-form>
    </div>
  </MyDialog>
  <PolyDialog
    v-if="polyDialogVisible"
    v-model="polyDialogVisible"
    :calib-data="signalModel.calib_data"
    :source="signalModel.name"
    @save="handleSave"
  />
  <TableDialog
    v-if="tableDialogVisible"
    v-model="tableDialogVisible"
    :calib-data="signalModel.calib_data"
    :source="signalModel.name"
    @save="handleSave"
  />
</template>

<script lang="ts" setup>
import { reactive, ref, inject, onMounted } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import $styles from '../index.module.scss'
import PolyDialog from '../polyDialog/index.vue'
import TableDialog from '../tableDialog/index.vue'
import { useBem } from '@/renderer/hooks/bem'
import { useVModel } from '@vueuse/core'
import { plcContextKey } from '../../type'
const { e } = useBem('plc-signal-signal', $styles)
const signalModel = reactive({
  name: '',
  signal_range: { min: 0, max: 0, unit: '' },
  scale_range: { min: 0, max: 0, unit: '' },
  scale_factor: 0,
  comments_str: '',
  comments: [''],
  calib_data: { type: 0, min: 0, max: 0, data: [0], raw_type: 0 }
})

const polyDialogVisible = ref(false)
const tableDialogVisible = ref(false)
const props = defineProps({
  params: {
    type: Object,
    default: () => ({})
  },
  modelShow: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelShow'])

const plcContext = inject(plcContextKey)

const isActive = useVModel(props, 'modelShow', emit)

const onSubmit = async () => {
  if (plcContext?.submitSignalData) {
    signalModel.comments = signalModel.comments_str.split('\n')
    await plcContext.submitSignalData({
      currentPlcIndex: props.params.currentPlcIndex,
      plcDeviceIndex: props.params.plcDeviceIndex,
      signalIndex: props.params.signalIndex,
      signalModel
    })
  }
  isActive.value = false
}

const handleCalculation = () => {
  if (signalModel.calib_data.type === 1) {
    polyDialogVisible.value = true
  } else if (signalModel.calib_data.type === 2) {
    tableDialogVisible.value = true
  }
}

const getDataInfo = async () => {
  const info =
    plcContext?.plcList.value[props.params.currentPlcIndex]?.devices?.[props.params.plcDeviceIndex]
      .signals.list[props.params.signalIndex]
  if (!info) return

  const {
    name = '',
    scale_range = { min: 0, max: 0, unit: '' },
    signal_range = { min: 0, max: 0, unit: '' },
    scale_factor = 0,
    comments = [''],
    calib_data = { type: 0, min: 0, max: 0, data: [0] }
  } = info

  signalModel.name = name
  signalModel.scale_range = { ...signalModel.scale_range, ...scale_range }
  signalModel.signal_range = { ...signalModel.signal_range, ...signal_range }
  signalModel.scale_factor = scale_factor
  signalModel.comments = comments
  signalModel.comments_str = comments.join('\n')
  signalModel.calib_data = { ...signalModel.calib_data, ...calib_data, raw_type: calib_data.type }
}

const handleSave = (calib_data: any) => {
  signalModel.calib_data = calib_data
  onSubmit()
}

onMounted(async () => {
  await getDataInfo()
})
</script>
