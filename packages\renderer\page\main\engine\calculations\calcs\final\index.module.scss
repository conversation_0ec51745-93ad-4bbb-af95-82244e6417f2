.box {
  height: 100%;
  padding: 5px 10px;

  &_search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;

    &_content {
      width: 260px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &_btn {
        width: 125px;
        height: 30px;
        text-align: center;
        line-height: 30px;
        border: 1px solid #bbbbbb;
        border-radius: 4px;
        cursor: pointer;
        &:active {
          background-color: #dcdfe6;
        }
      }
      span {
        display: inline-block;
        font-size: 13px;
        font-weight: bold;
      }
    }
  }
  &_table {
    width: 100%;
    height: calc(100% - 50px);
    margin: 5px 0;
    border: 1px solid #bbbbbb;
    background-color: #ffffff;
  }
}
.modelBox {
  display: flex;
  gap: 1px;

  &_btn {
    width: 98px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;

    &:last-child {
      margin-left: 6px;
    }
    &:active {
      background-color: #cecece;
      transform: scale(0.98);
      transition: transform 0.1s ease-in-out;
    }
  }
}
.tableBg {
  padding: 5px;
  background-color: #ffffff;
}
.form {
  &_box {
    display: flex;
    margin-bottom: 10px;
    &_content {
      width: 200px;
      display: flex;
      align-items: baseline;
      span {
        display: inline-block;
      }
    }
    &_change {
      width: auto;
      align-items: center;
    }
  }
}
