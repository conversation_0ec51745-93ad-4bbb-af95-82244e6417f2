import { defineComponent, PropType, ref, computed, watchEffect } from 'vue'
import { FormModel, TableType } from '../editRow'
import CodeEditor from '@/renderer/components/CodeEditor'
import $styles from './index.module.scss'
import { useBem } from '@/renderer/hooks'
import { useParameterDialog } from '@/renderer/utils/common'
export type EditRowCodeExpose = {
  codeTxt: string
}
export default defineComponent({
  name: 'EditRowCode',
  props: {
    formModel: {
      type: Object as PropType<FormModel>,
      required: true
    }
  },
  setup(props, { expose }) {
    const { b } = useBem('editrow', $styles)
    const codeTxt = ref('')
    const handleSelectParamter = (formModel: Record<string, any>, key: string) => {
      return async () => {
        const res = await useParameterDialog()
        formModel[key] = res
      }
    }
    const isThreeD = computed(() => props.formModel.tableType === TableType['3D'])
    watchEffect(() => {
      const {
        formModel: { corrData }
      } = props
      const maxLeftLen = Math.max(...corrData.map(item => `${item.coord1}`.length))
      const maxCenterLen = Math.max(...corrData.map(item => `${item.coord2}`.length))
      const spaceNum = 30
      codeTxt.value = corrData
        .map(item => {
          const leftLen = `${item.coord1}`.length
          const centerLen = `${item.coord2}`.length
          const centerStr = `${item.coord2}`.padStart(maxLeftLen - leftLen + spaceNum)
          const rightStr = item.coord3
            ? `${item.coord3}`.padStart(maxCenterLen - centerLen + spaceNum)
            : ''
          return `${item.coord1}${centerStr}${rightStr}`
        })
        .join('\n')
    })
    expose({
      codeTxt
    })
    return () => {
      const { output, xInput, yInput } = props.formModel
      return (
        <div class={b('code')}>
          <div class={b('code_header')}>
            <div>Output: {output}</div>
            <div>
              {`X Input: `}
              <wui-button onClick={handleSelectParamter(props.formModel, 'xInput')}>
                {xInput || 'None'}
              </wui-button>
            </div>
            {isThreeD.value && (
              <div>
                {`Y Input: `}
                <wui-button onClick={handleSelectParamter(props.formModel, 'yInput')}>
                  {yInput || 'None'}
                </wui-button>
              </div>
            )}
          </div>
          <CodeEditor v-model:code={codeTxt.value} height='100%' />
        </div>
      )
    }
  }
})
