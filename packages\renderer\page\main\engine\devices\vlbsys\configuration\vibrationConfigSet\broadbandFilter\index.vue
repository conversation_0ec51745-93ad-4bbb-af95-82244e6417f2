<template>
  <div>
    <h2>Broadband Filter Setup</h2>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Broadban Filter</h4>
        <wui-select v-model="currentIndex" placeholder="Select" @change="onFilterChange">
          <wui-option
            v-for="item in broadbandFilterOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Vibration Input</h4>
        <wui-select
          v-model="currentBroadband.vib_channel"
          placeholder="Select"
          @change="onChange('vib_channel', $event)"
        >
          <wui-option
            v-for="item in vibChannelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Upper Cutoff of filter</h4>
        <wui-input-number
          :model-value="currentBroadband.upper_cutoff"
          :min="2"
          :max="10000"
          clearable
          :controls="false"
          placeholder="Please input"
          @change="onChange('upper_cutoff', $event)"
        />
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Detector Type</h4>
        <wui-select
          v-model="currentBroadband.detector_type"
          placeholder="Select"
          @change="onChange('detector_type', $event)"
        >
          <wui-option
            v-for="item in detectorTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Lower Cutoff of filter</h4>
        <wui-input-number
          :model-value="currentBroadband.lower_cutoff"
          :min="1"
          :max="999"
          clearable
          :controls="false"
          placeholder="Please input"
          @change="onChange('lower_cutoff', $event)"
        />
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Output Units</h4>
        <wui-select
          v-model="currentBroadband.output_units"
          placeholder="Select"
          @change="onChange('output_units', $event)"
        >
          <wui-option
            v-for="item in outPutOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Filter Type</h4>
        <wui-select
          v-model="currentBroadband.filter_type"
          placeholder="Select"
          @change="onChange('filter_type', $event)"
        >
          <wui-option
            v-for="item in filterTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Filter Shape</h4>
        <wui-select
          v-model="currentBroadband.filter_shape"
          placeholder="Select"
          @change="onChange('filter_shape', $event)"
        >
          <wui-option
            v-for="item in filterShapeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Time Constant</h4>
        <wui-select
          v-model="currentBroadband.time_constant"
          placeholder="Select"
          @change="onChange('time_constant', $event)"
        >
          <wui-option
            v-for="item in timeConstantOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, watchEffect } from 'vue'
import styles from '../index.module.scss'
import { BizEngine } from '@/renderer/logic'
import { useCore, useHandler } from '@/renderer/hooks'
import { WuiMessage } from '@wuk/wui'
import { VibBandFilterOption } from '@wuk/cfg'

const homePtr = useCore<BizEngine>(BizEngine)
const currentIndex = ref<number>(0)
const currentBroadband = ref<VibBandFilterOption>({
  vib_channel: 1,
  detector_type: 0,
  time_constant: 0,
  output_units: 0,
  filter_shape: 0,
  filter_type: 0,
  upper_cutoff: 2,
  lower_cutoff: 1
})
const broadbandList = ref<VibBandFilterOption[]>([])

const props = defineProps({
  currentTableIndex: {
    type: Number,
    default: -1
  }
})

const broadbandFilterOptions = computed(() => {
  return broadbandList.value.map((_, index) => ({
    label: `${index + 1}`,
    value: index
  }))
})
const vibChannelOptions = [
  {
    label: '1',
    value: 0
  },
  {
    label: '2',
    value: 1
  },
  {
    label: '3',
    value: 2
  },
  {
    label: '4',
    value: 3
  }
]
const outPutOptions = [
  {
    label: 'Disabled',
    value: 0
  },
  {
    label: `Acceleration(g's)`,
    value: 1
  },
  {
    label: 'Velocity(IPS)',
    value: 2
  },
  {
    label: 'Displacement(Mils)',
    value: 3
  },
  {
    label: 'Acceleration(m/sec2)',
    value: 4
  },
  {
    label: 'Velocity(mm/sec)',
    value: 5
  },
  {
    label: 'Displacement(um)',
    value: 6
  }
]
const filterTypeOptions = [
  {
    label: 'No Filtering',
    value: 0
  },
  {
    label: 'Low Pass',
    value: 1
  },
  {
    label: 'High Pass',
    value: 2
  },
  {
    label: 'Band Pass',
    value: 3
  }
]
const filterShapeOptions = [
  {
    label: 'Rectangular',
    value: 0
  },
  {
    label: '7 Pole Chebyshev',
    value: 1
  },
  {
    label: '6 Pole Butterworth',
    value: 2
  },
  {
    label: 'User Defined',
    value: 3
  }
]
const timeConstantOptions = [
  {
    label: 'No Smoothing',
    value: 0
  },
  {
    label: '0.25 Sec Smoothing',
    value: 1
  },
  {
    label: '0.50 Sec Smoothing',
    value: 2
  },
  {
    label: '0.75 Sec Smoothing',
    value: 3
  },
  {
    label: '1.00 Sec Smoothing',
    value: 4
  },
  {
    label: '1.25 Sec Smoothing',
    value: 5
  },
  {
    label: '1.50 Sec Smoothing',
    value: 6
  }
]
const detectorTypeOptions = [
  {
    label: 'Peak Detector',
    value: 0
  },
  {
    label: 'RMS Detector',
    value: 2
  },
  {
    label: 'Average Detector',
    value: 3
  }
]
const onFilterChange = (index: number) => {
  currentIndex.value = index
  if (broadbandList.value[index]) {
    currentBroadband.value = { ...broadbandList.value[index] }
  }
}

const onChange = async (key: string, value: number) => {
  const result = await homePtr.value?.writeVibBandFilter(
    props.currentTableIndex,
    currentIndex.value,
    { [key]: value }
  )
  if (!result) return
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 80
  })
}

watch(currentIndex, newIndex => {
  if (broadbandList.value[newIndex]) {
    currentBroadband.value = { ...broadbandList.value[newIndex] }
  }
})

const getDataInfo = async () => {
  if (props.currentTableIndex === -1) return
  broadbandList.value =
    (await homePtr.value?.readVibBandFilter(props.currentTableIndex)) ||
    ([] as VibBandFilterOption[])
  currentBroadband.value = broadbandList.value[currentIndex.value] || {
    vib_channel: 1,
    detector_type: 0,
    time_constant: 0,
    output_units: 0,
    filter_shape: 0,
    filter_type: 0,
    upper_cutoff: 2,
    lower_cutoff: 1
  }
}

useHandler(homePtr, BizEngine.onBroadbandFilterSetupChanged, getDataInfo)

watchEffect(getDataInfo)
</script>
