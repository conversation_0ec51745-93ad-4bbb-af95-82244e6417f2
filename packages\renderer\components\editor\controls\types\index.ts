export type DataType = 'static' | 'dynamic'
export type ApiMethod = 'get' | 'post'
export type TextAlignType = 'left' | 'right' | 'center'
export type FontBoldType = 'lighter' | 'normal' | 'bold' | 'bolder'
export type LegendPosition =
  | 'top'
  | 'top-left'
  | 'top-right'
  | 'right'
  | 'right-top'
  | 'right-bottom'
  | 'left'
  | 'left-top'
  | 'left-bottom'
  | 'bottom'
  | 'bottom-left'
  | 'bottom-right'
export type LegendLayout = 'horizontal' | 'vertical'
export type LegendShape =
  | 'circle'
  | 'square'
  | 'line'
  | 'diamond'
  | 'triangle'
  | 'triangle-down'
  | 'hexagon'
  | 'bowtie'
  | 'cross'
  | 'plus'
  | 'hyphen'

export type ToggleType = string | boolean
export type BelongType = 'base' | ''

export interface BaseDefineType<T = string> extends Record<string, any> {
  key: string
  type: T
  name?: string
  belong?: BelongType
  comments?: string
  field?: string
  font?: number
  required?: boolean
  parent?: string
}

export interface UploadConfigType extends BaseDefineType<'Upload'> {
  isCrop?: boolean
  cropRate?: number
}
export type UploadDefaultType = Array<{
  uid: string
  name: string
  status: string
  url: string
}>

export interface TextConfigType extends BaseDefineType<'Text'> {
  dataType?: ToggleType
  toggle?: ToggleType
}
export type TextDefaultType = string

export interface DataConfigType extends BaseDefineType<'Data'> {
  toggle?: ToggleType
  dataType?: ToggleType
}
export type DataDefaultType = string

export interface TextAreaConfigType extends BaseDefineType<'TextArea'> {
  toggle?: ToggleType
  dataType?: ToggleType
}
export type TextAreaDefaultType = string

export interface NumberConfigType extends BaseDefineType<'Number'> {
  unit?: string
  range?: [number, number]
  step?: number
  toggle?: ToggleType
  dataType?: ToggleType
}
export type NumberDefaultType = number

export interface ImageConfigType extends BaseDefineType<'Image'> {
  range: string[]
  dataType?: string
}
export type ImageDefaultType = string

export interface IDataListConfigType extends BaseDefineType<'DataList'> {
  cropRate: number
}

export type TDataListDefaultTypeItem = {
  id: string
  title: string
  desc: string
  link: string
  type?: number
  imgUrl: UploadDefaultType
}
export type TDataListDefaultType = Array<TDataListDefaultTypeItem>

export interface ColorConfigType extends BaseDefineType<'Color'> {
  test?: string
}
export type ColorDefaultType = string

export interface ParamListConfigType extends BaseDefineType<'ParamList'> {
  test?: Array<string>
}
export type ListDefaultType = string

export interface ParamConfigType extends BaseDefineType<'Param'> {
  test?: string
}
export type ParamDefaultType = string

export interface FontConfigType extends BaseDefineType<'Font'> {
  test?: string
}
export type FontDefaultType = string

export interface FileConfigType extends BaseDefineType<'File'> {
  test?: string
}
export type FileDefaultType = string

export interface MultiColorConfigType extends BaseDefineType<'MultiColor'> {
  test?: string
}
export type MultiColorDefaultType = Array<string>

export interface RichTextConfigType extends BaseDefineType<'RichText'> {
  test?: string
}
export type RichTextDefaultType = string

export interface MutiTextConfigType extends BaseDefineType<'MutiText'> {
  test?: string
}
export type MutiTextDefaultType = Array<string>

export interface SelectConfigType<K extends string | number = string, T = any>
  extends BaseDefineType<'Select'> {
  range: Array<{
    key: K
    text: string
  }>
  toggle?: ToggleType
  dataType?: ToggleType
  childs?: Record<K, Array<T>>
}
export type SelectRangType<K> = K

export interface ObjectConfigType<T = any> extends BaseDefineType<'Object'> {
  childs?: Array<T>
}
export type ObjectDefaultType<K> = K

export interface RadioConfigType<K> extends BaseDefineType<'Radio'> {
  range: Array<{
    key: K
    text: string
  }>
  toggle?: ToggleType
  dataType?: ToggleType
}
export type RadioDefaultType<K> = K

export interface SwitchConfigType<T = string | number> extends BaseDefineType<'Switch'> {
  range: [T, T]
}
export type SwitchDefaultType<T = boolean> = T

export interface SliderConfigType extends BaseDefineType<'Slider'> {
  range: [number, number]
}
export type SliderDefaultType = number

export interface CardPickerConfigType<T> extends BaseDefineType<'CardPicker'> {
  icons: Array<T>
}
export type CardPickerDefaultType<T> = T

export interface TableConfigType<T = any> extends BaseDefineType<'Table'> {
  column: Array<string>
  row: Array<T>
}
export type TableDefaultType<T> = Array<Array<T>>

export interface PosConfigType extends BaseDefineType<'Pos'> {
  placeObj: {
    text: string
    link: string
  }
}
export type PosItem = number | undefined
export type PosDefaultType = [PosItem, PosItem]

export interface FormItemsConfigType extends BaseDefineType<'FormItems'> {
  test?: string
}
export type CollapseRangType = Array<
  | SelectConfigType<any>
  | UploadConfigType
  | TextConfigType
  | DataConfigType
  | TextAreaConfigType
  | NumberConfigType
  | ImageConfigType
  | ColorConfigType
  | MutiTextConfigType
  | RadioConfigType<any>
  | SwitchConfigType
  | TableConfigType
  | MultiColorConfigType
>
export interface CollapseConfigType extends BaseDefineType<'Collapse'> {
  range: CollapseRangType
}

export type baseFormOptionsType = {
  label: string
  value: string
}

export type baseFormTextTpl = {
  id: string
  type: 'Text'
  label: string
  placeholder: string
}

export type baseFormTextTipTpl = {
  id: string
  type: 'MyTextTip'
  label: string
  color: string
  fontSize: number
}

export type baseFormNumberTpl = {
  id: string
  type: 'Number'
  label: string
  placeholder: string
}

export type baseFormTextAreaTpl = {
  id: string
  type: 'Textarea'
  label: string
  placeholder: string
}

export type baseFormMyRadioTpl = {
  id: string
  type: 'MyRadio'
  label: string
  options: baseFormOptionsType[]
}

export type baseFormMyCheckboxTpl = {
  id: string
  type: 'MyCheckbox'
  label: string
  options: baseFormOptionsType[]
}

export type baseFormMySelectTpl = {
  id: string
  type: 'MySelect'
  label: string
  options: baseFormOptionsType[]
}

export type baseFormDateTpl = {
  id: string
  type: 'Date'
  label: string
  placeholder: string
}

export type baseFormUnion =
  | baseFormTextTpl
  | baseFormTextTipTpl
  | baseFormNumberTpl
  | baseFormTextAreaTpl
  | baseFormMyRadioTpl
  | baseFormMyCheckboxTpl
  | baseFormMySelectTpl
  | baseFormDateTpl
export type baseFormUnionType =
  | baseFormTextTpl['type']
  | baseFormTextTipTpl['type']
  | baseFormNumberTpl['type']
  | baseFormTextAreaTpl['type']
  | baseFormMyRadioTpl['type']
  | baseFormMyCheckboxTpl['type']
  | baseFormMySelectTpl['type']
  | baseFormDateTpl['type']

export type TFormItemsDefaultType = Array<baseFormUnion>

export enum ParamBoxType {
  None = 0,
  Outline,
  Filled
}

export enum WeightType {
  Bold = 0,
  Medium = 1
}

export enum SlantType {
  R = 0,
  O = 1
}

export enum OperatorType {
  Greater = '>',
  Less = '<',
  Equal = '='
}

export enum TicPositionType {
  Below = 'Below',
  Above = 'Above'
}
export enum BarDirectionType {
  Horizontal = 0,
  Vertical
}
