<template>
  <div>
    <h2>Vibration Input Setup</h2>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Vibration Input</h4>
        <wui-select v-model="currentIndex" placeholder="Select" @change="onVibInputChange">
          <wui-option
            v-for="item in vibInputOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Sensitivity (pC/g)</h4>
        <wui-input-number
          :model-value="currentVibInput.sensitivity"
          :min="1.0"
          input-align="left"
          :max="2000.0"
          clearable
          :precision="1"
          :controls="false"
          placeholder="Please input"
          @change="onChange('sensitivity', $event)"
        />
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Expected input range</h4>
        <wui-input-number
          :model-value="currentVibInput.input_range"
          :min="0.1"
          input-align="left"
          :max="150.0"
          clearable
          :precision="1"
          :controls="false"
          placeholder="Please input"
          @change="onChange('input_range', $event)"
        />
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Transducer type</h4>
        <wui-select
          v-model="currentVibInput.transducer_type"
          placeholder="Select"
          @change="onChange('transducer_type', $event)"
        >
          <wui-option
            v-for="item in transducerTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Gain of amplifier</h4>
        <wui-select
          v-model="currentVibInput.gain"
          placeholder="Select"
          @change="onChange('gain', $event)"
        >
          <wui-option
            v-for="item in amplifierGainOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>A/D gain</h4>
        <wui-select
          v-model="currentVibInput.ad_gain"
          placeholder="Select"
          @change="onChange('ad_gain', $event)"
        >
          <wui-option
            v-for="item in adFullScaleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Fast Fourier Transform (FFT)</h4>
        <wui-input
          v-model="currentVibInput.fft_size"
          placeholder="Please input"
          @change="onChange('fft_size', $event)"
        />
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Sampling Rate</h4>
        <wui-input
          v-model="currentVibInput.sampling_rate"
          placeholder="Please input"
          @change="onChange('sampling_rate', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watchEffect, watch } from 'vue'
import styles from '../index.module.scss'
import { useBizEngine, useHandler } from '@/renderer/hooks'
import { WuiMessage } from '@wuk/wui'
import { VibInputOption } from '@wuk/cfg'
import { BizEngine } from '@/renderer/logic'

const homePtr = useBizEngine()
const currentIndex = ref<number>(0)
const currentVibInput = ref<VibInputOption>({
  sensitivity: 1.0,
  gain: 0,
  input_range: 0.1,
  ad_gain: 0,
  transducer_type: 0,
  fft_size: 0,
  sampling_rate: 0
})
const vibInputList = ref<VibInputOption[]>([])

const props = defineProps({
  currentTableIndex: {
    type: Number,
    default: -1
  }
})

const vibInputOptions = computed(() => {
  return vibInputList.value.map((_, index) => ({
    label: `${index + 1}`,
    value: index
  }))
})

const transducerTypeOptions = [
  {
    label: 'Disabled',
    value: 0
  },
  {
    label: 'Single-ended Accel',
    value: 1
  },
  {
    label: 'Differential Accel',
    value: 2
  },
  {
    label: 'Velocity',
    value: 3
  },
  {
    label: 'ICP Accel',
    value: 4
  }
]

const amplifierGainOptions = [
  {
    label: 'Vib. System will select gain',
    value: 0
  },
  {
    label: 'Gain of 1',
    value: 1
  },
  {
    label: 'Gain of 10',
    value: 10
  }
]

const adFullScaleOptions = [
  {
    label: 'Auto Select',
    value: 0
  },
  {
    label: '10.00 volts',
    value: 1
  },
  {
    label: '5.00 volts',
    value: 2
  },
  {
    label: '2.50 volts',
    value: 4
  },
  {
    label: '1.25 volts',
    value: 8
  }
]

const onVibInputChange = (index: number) => {
  currentIndex.value = index
  if (vibInputList.value[index]) {
    currentVibInput.value = { ...vibInputList.value[index] }
  }
}

const onChange = async (key: string, value: string | number) => {
  const result = await homePtr.value?.writeVibInput(props.currentTableIndex, currentIndex.value, {
    [key]: value
  })
  if (!result) return
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 80
  })
}

watch(currentIndex, newIndex => {
  if (vibInputList.value[newIndex]) {
    currentVibInput.value = { ...vibInputList.value[newIndex] }
  }
})

const getDataInfo = async () => {
  if (props.currentTableIndex === -1) return
  vibInputList.value =
    (await homePtr.value?.readVibInput(props.currentTableIndex)) || ([] as VibInputOption[])
  currentVibInput.value = vibInputList.value[currentIndex.value] || {
    sensitivity: 1.0,
    gain: 0,
    input_range: 0.1,
    ad_gain: 0,
    transducer_type: 0,
    fft_size: 0,
    sampling_rate: 0
  }
}

useHandler(homePtr, BizEngine.onVibrationInputSetupChanged, getDataInfo)

watchEffect(getDataInfo)
</script>
