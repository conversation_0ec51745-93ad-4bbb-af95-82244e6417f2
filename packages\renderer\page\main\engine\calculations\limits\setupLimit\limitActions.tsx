import { useBem } from '@/renderer/hooks'
import { defineComponent, ref, useModel, watch } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import ColorSelect from '@/renderer/components/ColorSelect/index.tsx'
import $styles from './index.module.scss'
import { useParameterDialog } from '@/renderer/utils/common'
import { CfgLimtPartial, RateLimtPartial } from '../type'
export default defineComponent({
  name: 'LimitActions',
  props: {
    showLimitActions: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => ({})
    },
    onUpdateRow: {
      type: Function,
      default: undefined
    },
    dataType: {
      type: String,
      default: 'table'
    }
  },
  emits: ['update:showLimitActions'],
  setup(props, { emit }) {
    const showLimitActions = useModel(props, 'showLimitActions')

    const d1: CfgLimtPartial = {
      phase: 'None',
      alarm: 'No Alarm',
      color: 'Red',
      normality: 0,
      store_event: true,
      above_message: 'DEFAULT',
      below_message: 'DEFAULT'
    }

    const d2: RateLimtPartial = {
      phase: 'None',
      alarm: 'No Alarm',
      exceeded_message: 'DEFAULT',
      back_message: 'DEFAULT',
      color: 'Red',
      store_event: true
    }

    const { e, b, m } = useBem('setup-limit-actions', $styles)

    const model = ref<any>({})

    watch(
      showLimitActions,
      (val: boolean) => {
        if (val) {
          if (props.dataType === 'table') {
            model.value = { ...d1, ...props.rowData }
          } else {
            model.value = { ...d2, ...props.rowData }
          }
        }
      },
      { immediate: false }
    )

    const handleOk = () => {
      if (props.onUpdateRow) {
        props.onUpdateRow({ ...model.value })
      }
      showLimitActions.value = false
    }

    const alarmOption = [
      { label: 'No Alarm', value: 'No Alarm' },
      { label: 'Replay Based Alarm', value: 'Replay Based Alarm' },
      { label: 'mysound', value: 'mysound' },
      { label: 'mysound2', value: 'mysound2' },
      { label: 'calc1lim', value: 'calc1lim' },
      { label: 'cadintro', value: 'cadintro' },
      { label: 'ding', value: 'ding' }
    ]
    const normality = [
      { label: 'Normal Band', value: 0 },
      { label: 'Abnormal Band', value: 1 }
    ]
    const handleSelectParam = async () => {
      const res = await useParameterDialog()
      model.value.phase = res
    }
    return () => (
      <div class={b()}>
        <MyDialog
          title='Limit Actions'
          contentStyle={{ backgroundColor: '#fff', padding: '20px 20px 0' }}
          width='500px'
          v-model={showLimitActions.value}
          onOk={handleOk}>
          <div class={e('body')}>
            <wui-form
              label-width='230'
              validate-box-style='fill'
              validate-msg-position='right'
              validate-ellipsis='2'
              hide-required-asterisk
              label-position='top'
              model={model.value}
              status-icon>
              {props.dataType === 'table' ? (
                <>
                  <wui-form-item label='Enter Range From Below Message' prop='below_message'>
                    <wui-input
                      v-model={model.value.below_message}
                      type='text'
                      placeholder='Enter Range From Below Message'
                    />
                  </wui-form-item>
                  <wui-form-item label='Enter Range From Above Message' prop='above_message'>
                    <wui-input
                      v-model={model.value.above_message}
                      type='text'
                      placeholder='Enter Range From Above Message'
                    />
                  </wui-form-item>
                </>
              ) : (
                <>
                  <wui-form-item
                    label='Exceeded Rate-of-change Limit Message'
                    prop='exceeded_message'>
                    <wui-input
                      v-model={model.value.exceeded_message}
                      type='text'
                      placeholder='Exceeded Message'
                    />
                  </wui-form-item>
                  <wui-form-item
                    label='Back Within Rate-of-change Limit Message'
                    prop='back_message'>
                    <wui-input
                      v-model={model.value.back_message}
                      type='text'
                      placeholder='Back Message'
                    />
                  </wui-form-item>
                </>
              )}
              <div class={e('form', 'inline')}>
                <wui-form-item label='phase' prop='phase'>
                  <div>
                    <wui-button bg text type='primary' onClick={handleSelectParam}>
                      {model.value.phase}
                    </wui-button>
                  </div>
                </wui-form-item>
                <wui-form-item label='Color' prop='color'>
                  <ColorSelect v-model={model.value.color} />
                </wui-form-item>
                <wui-form-item label='Alarm' prop='alarm'>
                  <wui-select v-model={model.value.alarm} placeholder='Select alarm'>
                    {alarmOption.map(it => (
                      <wui-option label={it.label} value={it.value} />
                    ))}
                  </wui-select>
                </wui-form-item>
              </div>
              <div class={e('form', 'inline')}>
                {props.dataType === 'table' && (
                  <wui-form-item label='Normality' prop='normality'>
                    <wui-select v-model={model.value.normality} placeholder='select normality'>
                      {normality.map(it => (
                        <wui-option label={it.label} value={it.value} />
                      ))}
                    </wui-select>
                  </wui-form-item>
                )}
                <wui-form-item label='Store Event' prop='store_event'>
                  <wui-switch v-model={model.value.store_event} />
                </wui-form-item>
              </div>
            </wui-form>
          </div>
        </MyDialog>
      </div>
    )
  }
})
