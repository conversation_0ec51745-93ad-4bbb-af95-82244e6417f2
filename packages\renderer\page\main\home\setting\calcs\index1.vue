<template>
  <div>
    <h2>System Calcs</h2>
    <div :class="styles.mb_5">
      <wui-table
        border
        :data="tableData1"
        :class="styles.grid"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
      >
        <wui-table-column prop="name" label="Group Name" align="center" show-overflow-tooltip />
        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default>
            <wui-icon @click="openModel('tableIcon')">
              <EditPen />
            </wui-icon>
          </template>
        </wui-table-column>
      </wui-table>
    </div>

    <MyDialog
      v-model="aModelShow"
      title="Calculation Group: TLA_PLA_Coeffs"
      width="600px"
      @ok="openModel('submit')"
    >
      <div class="tabsBox">
        <wui-button>Operators</wui-button>
        <wui-button style="margin-left: 6px">Functions</wui-button>
        <wui-button style="margin-left: 6px">Parameters</wui-button>
      </div>
      <div :class="styles.tableBg">
        <wui-table
          v-if="btnShow1"
          :data="tableData2"
          height="200px"
          border
          style="width: 100%"
          :header-cell-style="{
            background: '#EAF1FD',
            color: '#90AFE4',
            fontSize: '18px',
            fontWeight: 'bold'
          }"
        >
          <wui-table-column prop="name" label="Name" align="center" show-overflow-tooltip />
          <wui-table-column prop="equation" label="Equation" align="center" show-overflow-tooltip />
          <wui-table-column prop="comments" label="Comments" align="center" show-overflow-tooltip />
          <wui-table-column prop="units" label="Units" align="center" show-overflow-tooltip />
        </wui-table>
        <wui-input
          v-else
          v-model="plaInput"
          placeholder="Please input"
          type="textarea"
          style="width: 100%; margin-left: 0"
          resize="none"
          :rows="9"
        />
      </div>
      <div :class="styles.modelBox">
        <div
          :class="styles.modelBox_btn"
          :style="{ background: btnShow1 ? '#ffffff' : '#cecece' }"
          @click="onChangeBtn(1)"
        >
          Column List
        </div>
        <div
          :class="styles.modelBox_btn"
          :style="{ background: btnShow2 ? '#ffffff' : '#cecece' }"
          @click="onChangeBtn(2)"
        >
          Text
        </div>
      </div>
    </MyDialog>
  </div>
</template>

<script lang="ts" setup>
import MyDialog from '@/renderer/components/dialog/index.vue'
import { EditPen } from '@element-plus/icons-vue'
import { ref } from 'vue'
import styles from '../index.module.scss'

const plaInput = ref('')
const tableData1: any = [
  // {
  //   name: ''
  // }
  // {
  //   name: 'PLAMap'
  // },
  // {
  //   name: 'PLAMap'
  // }
]
const tableData2 = [
  {
    name: 'PLA_Idle',
    equation: 0.4,
    comments: 'Comments',
    Units: ''
  },
  {
    name: 'PLA_Idle',
    equation: 0.4,
    comments: 'Comments',
    Units: ''
  },
  {
    name: 'PLA_Idle',
    equation: 0.4,
    comments: 'Comments',
    Units: ''
  }
]

const aModelShow = ref(false)
const btnShow1 = ref(true)
const btnShow2 = ref(false)

const openModel = (type: string) => {
  if (type === 'tableIcon') {
    aModelShow.value = true
  } else {
    aModelShow.value = false
  }
  btnShow1.value = true
  btnShow2.value = false
}

const onChangeBtn = (i: number) => {
  if (i === 1) {
    btnShow1.value = true
    btnShow2.value = false
  } else {
    btnShow1.value = false
    btnShow2.value = true
  }
}
</script>

<style lang="scss" scoped>
.tabsBox {
  :deep(.wui-button) {
    width: auto;
    margin-bottom: 10px;
    color: #181010;
    --wui-button-hover-border-color: #d3d3d3;
    --wui-button-hover-bg-color: rgba(250, 250, 250, 0.1);
    --wui-button-hover-text-color: #181010;
    --wui-button-active-border-color: #dcdfe6;
  }
}
</style>
