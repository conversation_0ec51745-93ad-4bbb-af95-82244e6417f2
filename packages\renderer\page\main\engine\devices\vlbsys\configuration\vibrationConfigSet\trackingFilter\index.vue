<template>
  <div>
    <h2>Tracking Filter Setup</h2>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Tracking Filter</h4>
        <wui-select v-model="currentIndex" placeholder="Select" @change="onTrackingFilterChange">
          <wui-option
            v-for="item in trackingFilterOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Vibration Input</h4>
        <wui-select
          v-model="currentTracking.vib_channel"
          placeholder="Select"
          @change="onChange('vib_channel', $event)"
        >
          <wui-option
            v-for="item in vibTachChannelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Output Units</h4>
        <wui-select
          v-model="currentTracking.output_units"
          placeholder="Select"
          @change="onChange('output_units', $event)"
        >
          <wui-option
            v-for="item in outPutUnitOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Tachometer Input</h4>
        <wui-select
          v-model="currentTracking.tach_channel"
          placeholder="Select"
          @change="onChange('tach_channel', $event)"
        >
          <wui-option
            v-for="item in vibTachChannelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Filter Spec.</h4>
        <wui-select
          v-model="currentTracking.filter_specification"
          placeholder="Select"
          @change="onChange('filter_specification', $event)"
        >
          <wui-option
            v-for="item in filterSpecificationOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Detector Type</h4>
        <wui-select
          v-model="currentTracking.detector_type"
          placeholder="Select"
          @change="onChange('detector_type', $event)"
        >
          <wui-option
            v-for="item in detectorTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Full Scale Output in detector units</h4>
        <wui-input-number
          :model-value="currentTracking.full_scale_units"
          :min="0.1"
          :max="150.0"
          :precision="1"
          clearable
          :controls="false"
          placeholder="Please input"
          @change="onChange('full_scale_units', $event)"
        />
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Full Scale Output in Volts</h4>
        <wui-input-number
          :model-value="currentTracking.full_scale_volts"
          :min="1.0"
          :max="10.0"
          :precision="1"
          clearable
          :controls="false"
          placeholder="Please input"
          @change="onChange('full_scale_volts', $event)"
        />
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Band Width</h4>
        <wui-select
          v-model="currentTracking.band_width"
          placeholder="Select"
          @change="onChange('band_width', $event)"
        >
          <wui-option
            v-for="item in bandWidthOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Filter Q.</h4>
        <wui-select
          v-model="currentTracking.filter_q"
          placeholder="Select"
          @change="onChange('filter_q', $event)"
        >
          <wui-option
            v-for="item in filterQOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Time Constant</h4>
        <wui-select
          v-model="currentTracking.time_constant"
          placeholder="Select"
          @change="onChange('time_constant', $event)"
        >
          <wui-option
            v-for="item in timeConstantOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </wui-select>
      </div>
    </div>
    <div :class="styles.mb_5">
      <div :class="styles.extension">
        <h4>Order Tracking</h4>
        <wui-input-number
          :model-value="currentTracking.order_tracking"
          :min="0.1"
          :max="2000.0"
          :precision="1"
          clearable
          :controls="false"
          placeholder="Please input"
          @change="onChange('order_tracking', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, watchEffect } from 'vue'
import styles from '../index.module.scss'
import { BizEngine } from '@/renderer/logic'
import { useCore, useHandler } from '@/renderer/hooks'
import { WuiMessage } from '@wuk/wui'
import { VibTFInputOption } from '@wuk/cfg'

const homePtr = useCore<BizEngine>(BizEngine)

const currentIndex = ref<number>(0)

const currentTracking = ref<VibTFInputOption>({
  vib_channel: 0,
  tach_channel: 0,
  output_units: 0,
  detector_type: 0,
  full_scale_units: 0.1,
  full_scale_volts: 1.0,
  filter_specification: 0,
  filter_q: 0,
  band_width: 0,
  time_constant: 0,
  order_tracking: 0.1
})

const trackingFilterList = ref<VibTFInputOption[]>([])

const props = defineProps({
  currentTableIndex: {
    type: Number,
    default: -1
  }
})

const trackingFilterOptions = computed(() => {
  return trackingFilterList.value.map((_, index) => ({
    label: `${index + 1}`,
    value: index
  }))
})

const onTrackingFilterChange = (index: number) => {
  currentIndex.value = index
  if (trackingFilterList.value[index]) {
    currentTracking.value = { ...trackingFilterList.value[index] }
  }
}

watch(currentIndex, newIndex => {
  if (trackingFilterList.value[newIndex]) {
    currentTracking.value = { ...trackingFilterList.value[newIndex] }
  }
})
const vibTachChannelOptions = [
  {
    label: '1',
    value: 0
  },
  {
    label: '2',
    value: 1
  },
  {
    label: '3',
    value: 2
  },
  {
    label: '4',
    value: 3
  }
]
const outPutUnitOptions = [
  {
    label: 'Disabled',
    value: 0
  },
  {
    label: `Acceleration(g's)`,
    value: 1
  },
  {
    label: 'Velocity(IPS)',
    value: 2
  },
  {
    label: 'Displacement(Mils)',
    value: 3
  },
  {
    label: 'Acceleration(m/sec2)',
    value: 4
  },
  {
    label: 'Displacement(um)',
    value: 5
  }
]
const filterSpecificationOptions = [
  {
    label: 'Constant Bandwidth',
    value: 0
  },
  {
    label: 'Constant Q',
    value: 1
  }
]
const detectorTypeOptions = [
  {
    label: 'Peak',
    value: 0
  },
  {
    label: 'Peak To Peak',
    value: 1
  },
  {
    label: 'RMS',
    value: 2
  },
  {
    label: 'Average',
    value: 3
  }
]
const filterQOptions = [
  {
    label: 'Q = 10',
    value: 10
  },
  {
    label: 'Q = 20',
    value: 20
  },
  {
    label: 'Q = 30',
    value: 30
  }
]

const bandWidthOptions = [
  {
    label: '5 Hz Bandwidth',
    value: 5
  },
  {
    label: '11 Hz Bandwidth',
    value: 11
  }
]
const timeConstantOptions = [
  {
    label: 'No Smoothing',
    value: 0
  },
  {
    label: '0.25 Sec Smoothing',
    value: 1
  },
  {
    label: '0.50 Sec Smoothing',
    value: 2
  },
  {
    label: '0.75 Sec Smoothing',
    value: 3
  },
  {
    label: '1.00 Sec Smoothing',
    value: 4
  },
  {
    label: '1.25 Sec Smoothing',
    value: 5
  },
  {
    label: '1.50 Sec Smoothing',
    value: 6
  }
]
const onChange = async (key: string, value: string | number) => {
  const result = await homePtr.value?.writeVibTFInput(props.currentTableIndex, currentIndex.value, {
    [key]: value
  })
  if (!result) return

  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 80
  })
}

const getDataInfo = async () => {
  if (props.currentTableIndex === -1) return
  trackingFilterList.value =
    (await homePtr.value?.readVibTFInput(props.currentTableIndex)) || ([] as VibTFInputOption[])
  currentTracking.value = trackingFilterList.value[currentIndex.value] || {
    vib_channel: 0,
    tach_channel: 0,
    output_units: 0,
    detector_type: 0,
    full_scale_units: 0.1,
    full_scale_volts: 1.0,
    filter_specification: 0,
    filter_q: 0,
    band_width: 0,
    time_constant: 0,
    order_tracking: 0.1
  }
}

useHandler(homePtr, BizEngine.onTrackingFilterSetupChanged, getDataInfo)

watchEffect(getDataInfo)
</script>
