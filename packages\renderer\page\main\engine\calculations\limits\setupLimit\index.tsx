import { defineAsyncComponent, defineComponent, ref, useModel } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { useBem, useBizEngine } from '@/renderer/hooks'
import $styles from './index.module.scss'
import LimitTable from './limitTable'
import LimitRateTable from './limitRateTable'
import { WuiButton } from '@wuk/wui'
import { cloneFnJSON } from '@vueuse/core'
import { useParameterDialog } from '@/renderer/utils/common'
import { currentParameters, SetupLimitExpose } from '../type'
const LimitActions = defineAsyncComponent(() => import('./limitActions'))

export default defineComponent({
  name: 'SetupLimit',
  props: {
    showSetupLimit: {
      type: Boolean,
      default: false
    },
    currentParameter: {
      type: Object as () => currentParameters,
      default: () => {}
    }
  },
  emits: ['update:showSetupLimit'],
  setup(props) {
    const startTableRef = ref<SetupLimitExpose>()
    const runTableRef = ref<SetupLimitExpose>()
    const startRateTableRef = ref<SetupLimitExpose>()
    const runRateTableRef = ref<SetupLimitExpose>()
    const bizEngine = useBizEngine()
    const showSetupLimit = useModel(props, 'showSetupLimit')
    const currentParameter = useModel(props, 'currentParameter')
    const showLimitActions = ref(false)
    const currentRow = ref<any>(null)
    const dataType = ref('table')
    const { e, b } = useBem('setup-limit', $styles)

    const handleSelectParam = async () => {
      const res = await useParameterDialog()
      startTableRef.value?.handleSelect(res)
    }

    const handleSetupLimitAction = (row: any, type: string) => {
      currentRow.value = row
      showLimitActions.value = true
      dataType.value = type
    }

    const handleUpdateRow = (newData: any) => {
      Object.assign(currentRow.value, newData)
    }

    const handleSave = async () => {
      const startLimits = cloneFnJSON(startTableRef.value?.getData()) || []
      const startRates = cloneFnJSON(startRateTableRef.value?.getData()) || []
      const runLimits = cloneFnJSON(runTableRef.value?.getData()) || []
      const runRates = cloneFnJSON(runRateTableRef.value?.getData()) || []

      const paramIndex = currentParameter.value.index as number

      await bizEngine.value?.modifyLimitParameter(paramIndex, {
        start: {
          cfg_list: startLimits,
          rate_list: startRates
        },
        run: {
          cfg_list: runLimits,
          rate_list: runRates
        }
      })
      showSetupLimit.value = false
    }

    const tableRefs = {
      start: { table: startTableRef, rate: startRateTableRef },
      run: { table: runTableRef, rate: runRateTableRef }
    }
    const tabList = [
      { key: 'start', label: 'Start Limits' },
      { key: 'run', label: 'Run Limits' }
    ]
    const renderTabPane = (type: 'start' | 'run') => (
      <wui-tab-pane
        class={e('body', 'tabs', 'pane')}
        label={tabList.find(t => t.key === type)?.label}>
        <div class={e('body', 'table')}>
          <LimitTable
            ref={tableRefs[type].table}
            limitType={type}
            currentParameter={currentParameter.value}
            onLimit-actions={handleSetupLimitAction}
            onAdd-table-row={handleSelectParam}
          />
          <LimitRateTable
            ref={tableRefs[type].rate}
            limitType={type}
            currentParameter={currentParameter.value}
            onLimit-actions={handleSetupLimitAction}
          />
        </div>
      </wui-tab-pane>
    )

    return () => (
      <>
        <MyDialog
          class={b()}
          contentStyle={{ padding: '0px' }}
          v-model={showSetupLimit.value}
          title={'Limit Editor: ' + (currentParameter.value.parameter?.param_id || '')}
          width='900px'
          onOk={handleSave}>
          {{
            'header-right': () => (
              <WuiButton class={e('header', 'btn')} onClick={handleSelectParam}>
                Parameters
              </WuiButton>
            ),
            default: () => (
              <div class={e('body')}>
                <wui-tabs type='border-card' class={e('body', 'tabs')}>
                  {tabList.map(tab => renderTabPane(tab.key as 'start' | 'run'))}
                </wui-tabs>
              </div>
            )
          }}
        </MyDialog>
        <LimitActions
          v-model:showLimitActions={showLimitActions.value}
          v-model:rowData={currentRow.value}
          dataType={dataType.value}
          onUpdateRow={handleUpdateRow}
        />
      </>
    )
  }
})
