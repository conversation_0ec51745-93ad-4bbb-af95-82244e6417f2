import { useParameterDialog } from '@/renderer/utils/common'
import { type FunctionalComponent } from 'vue'
import OperatorsButton from '@/renderer/components/OperatorsButton/index.vue'
import FunctionsButton from '@/renderer/components/FunctionsButton/index.vue'
import { useBem } from '@/renderer/hooks'
import $styles from './index.module.scss'
type EquationHeaderEmits = {
  select(val: string): void
}
export const EquationHeader: FunctionalComponent<any, EquationHeaderEmits> = (
  _,
  { emit, slots }
) => {
  const { e } = useBem('calc-tool', $styles)
  const handleSelectParam = async () => {
    const res = await useParameterDialog()
    handleSelect(res)
  }
  const handleSelect = (val: string) => {
    emit('select', val)
  }
  return (
    <div class={e('header')}>
      <div>
        <OperatorsButton onSelect={handleSelect} position='absolute' />
      </div>
      {slots.default?.()}
      <div>
        <FunctionsButton onSelect={handleSelect} />
        <wui-button onClick={handleSelectParam}>Parameters</wui-button>
      </div>
    </div>
  )
}
