import { DspDigitalDir, DspDigitalParamBox, DspDigitalType } from '@wuk/wui'
import { baseConfig, BaseConfigType, baseDefault } from '../common'
import {
  ColorConfigType,
  ParamBoxType,
  ParamConfigType,
  SelectConfigType,
  SliderConfigType,
  TableConfigType,
  TextConfigType,
  WeightType
} from '../types'

export type DspDigitalConfigType =
  | BaseConfigType
  | ColorConfigType
  | SliderConfigType
  | ParamConfigType
  | TextConfigType
  | SelectConfigType<number | string>

// label_space: number  // 组件未实现
// unit_space: number
// spacing: number
// param_box: number
// param_box_color: string
// label_color: string
// shading: number
// direction: number
// font_size: number    // 组件未实现
// font_weight: number
// label_font_size: number
// label_font_weight: number
// unit_font_size: number
// unit_font_weight: number
// digit_radius: string // todo：reader待实现
// item_vec: Array<DigitalItem>

//  DigitalItem {
//   param_id: string
//   type: string
//   width: number
//   prec: number
//   label: string
//   units: string
// }
export const dspDigitalConfig: Array<DspDigitalConfigType | TableConfigType<DspDigitalConfigType>> =
  [
    ...baseConfig,
    {
      key: 'digit_radius',
      name: 'Digit Radius',
      type: 'Slider',
      field: 'digitRadius',
      range: [0, 10],
      comments: 'Digit box的圆角弧度'
    },
    {
      key: 'label_space',
      name: 'Label Spacing',
      type: 'Number',
      field: 'labelSpace',
      range: [0, 33],
      comments: 'label值与数字值框之间的间距'
    },
    {
      key: 'unit_space',
      name: 'Unit Spacing',
      type: 'Number',
      field: 'unitSpace',
      range: [0, 33],
      comments: '单位与数字值之间的间距'
    },
    {
      key: 'spacing',
      name: 'Digital Spacing',
      type: 'Slider',
      field: 'spacing',
      range: [0, 100],
      comments: '横向：下边距，纵向：右边距'
    },
    {
      key: 'param_box',
      name: 'Parambox',
      type: 'Select',
      field: 'paramBox',
      range: [
        { key: DspDigitalParamBox.NONE, text: 'None' },
        { key: DspDigitalParamBox.OUTLINE, text: 'Outline' },
        { key: DspDigitalParamBox.FILLED, text: 'Filled' }
      ],
      comments:
        '0 1 2 【0:none 无 1:outline 边框 2:filled 填充（边框加背景色）】，用于控制显示数字值框的样式'
    },
    {
      key: 'param_box_color',
      name: 'Parambox Color',
      type: 'Color',
      field: 'paramBoxColor',
      comments: '控制数字值框的背景颜色或边框颜色'
    },
    {
      key: 'label_color',
      name: 'label color',
      type: 'Color',
      field: 'labelColor',
      comments: 'Label值的颜色'
    },
    {
      key: 'shading',
      name: 'Shading',
      type: 'Slider',
      field: 'shading',
      range: [0, 10],
      comments: '边框或背景颜色'
    },
    {
      key: 'direction',
      name: 'Direction',
      type: 'Select',
      field: 'dir',
      range: [
        { key: DspDigitalDir.HORIZONTAL, text: 'Horizontal' },
        { key: DspDigitalDir.VERTICAL, text: 'Vertical' }
      ],
      comments: '数字显示方向'
    },
    {
      key: 'font_size',
      name: 'Font Size',
      type: 'Slider',
      field: 'digitFontSize',
      range: [5, 100],
      comments: ''
    },
    {
      key: 'font_weight',
      name: 'Weight',
      type: 'Slider',
      range: [100, 900],
      field: 'digitFontWeight',
      step: 100,
      comments: ''
    },
    {
      key: 'label_font_size',
      name: 'Label Font Size',
      type: 'Slider',
      field: 'labelFontSize',
      range: [5, 100],
      comments: 'Label值的字体大小'
    },
    {
      key: 'label_font_weight',
      name: 'Label Font Weight',
      type: 'Slider',
      field: 'labelFontWeight',
      range: [5, 100],
      comments: 'Label值的字体粗细'
    },
    {
      key: 'unit_font_size',
      name: 'Unit Font Size',
      type: 'Slider',
      field: 'unitFontSize',
      range: [5, 100],
      comments: '单位值的字体粗细'
    },
    {
      key: 'unit_font_weight',
      name: 'Unit Font Weight',
      type: 'Slider',
      field: 'unitFontWeight',
      range: [100, 900],
      step: 100,
      comments: '单位值的字体大小'
    },
    {
      key: 'item_vec',
      name: 'Digital List',
      type: 'Table',
      field: 'digitalList',
      column: ['Parameter', 'Type', 'Width', 'Precision', 'Label', 'Units'],
      row: [
        {
          key: 'param_id',
          type: 'Param',
          comments: '参数Id'
        },
        {
          key: 'type',
          type: 'Select',
          range: [
            { key: DspDigitalType.DEFAULT, text: 'DEFAULT' },
            { key: DspDigitalType.FLOAT, text: 'FLOAT' },
            { key: DspDigitalType.INT, text: 'INTEGER' },
            { key: DspDigitalType.HEX, text: 'HEX' },
            { key: DspDigitalType.EXPONENT, text: 'EXPONENT' },
            { key: DspDigitalType.HOURS, text: 'HOURS' },
            { key: DspDigitalType.SECONDS, text: 'SECONDS' },
            { key: DspDigitalType.TIMER, text: 'TIMER' }
          ],
          comments: 'digital显示的格式'
        },
        {
          key: 'width',
          type: 'Slider',
          field: 'width',
          range: [0, 15],
          comments: '数字显示的总宽度'
        },
        {
          key: 'prec',
          type: 'Slider',
          field: 'prec',
          range: [0, 6],
          comments: '小数点后的位数'
        },
        {
          key: 'label',
          type: 'Text',
          field: 'label',
          comments: 'Label值'
        },
        {
          key: 'units',
          type: 'Text',
          field: 'unit',
          comments: '单位'
        }
      ],
      comments: ''
    }
  ]
export const dspDigitalDefault: Record<string, any> = {
  ...baseDefault,
  width: 350,
  height: 30,
  digit_radius: 5,
  label_space: 5,
  unit_space: 5,
  spacing: 5,
  param_box: DspDigitalParamBox.NONE,
  param_box_color: 'Black',
  label_color: 'Black',
  shading: 0,
  dir: DspDigitalDir.HORIZONTAL,
  font_size: 23,
  font_weight: 400,
  label_font_size: 23,
  label_font_weight: 400,
  unit_font_size: 23,
  unit_font_weight: 400,
  item_vec: [
    {
      param_id: 'None',
      type: DspDigitalType.DEFAULT,
      width: 8,
      prec: 2,
      label: 'Default',
      units: 'Default'
    }
  ]
}
