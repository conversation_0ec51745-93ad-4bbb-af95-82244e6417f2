import { DspBoxStyle } from '@wuk/wui'
import { baseConfig, BaseConfigType, baseDefault } from '../common'
import { ColorConfigType, SelectConfigType, SliderConfigType } from '../types'

export type DspBoxConfigType = BaseConfigType | ColorConfigType | SliderConfigType
// style: number
// box_color: string
// shading: number  // 组件未实现
// line_width: number
// line_color: string
// box_radius: string // todo：reader待实现
export const dspBoxConfig: Array<DspBoxConfigType | SelectConfigType<number, DspBoxConfigType>> = [
  ...baseConfig,
  {
    key: 'box_radius',
    name: 'Box Radius',
    type: 'Slider',
    field: 'boxRadius',
    range: [0, 500],
    comments: '盒子的圆角弧度'
  },
  {
    key: 'style',
    name: 'Box Type',
    type: 'Select',
    field: 'boxType',
    comments: '1：Frame 有边框和阴影 2：3-D 盒子有背景 3：Filled Frame 各种都有，可以自由配置',
    range: [
      { key: DspBoxStyle.FRAME, text: 'Frame' },
      { key: DspBoxStyle['3-D'], text: '3-D' },
      { key: DspBoxStyle['FILLED-FRAME'], text: 'Filled Frame' }
    ],
    childs: {
      [DspBoxStyle.FRAME]: [
        {
          key: 'line_width',
          name: 'Line Width',
          type: 'Slider',
          field: 'lineWidth',
          range: [0, 10],
          comments: '线条的厚度。即边框的宽度。'
        },
        {
          key: 'line_color',
          name: 'Line Color',
          type: 'Color',
          field: 'lineColor',
          comments: '线条的颜色'
        }
      ],
      [DspBoxStyle['3-D']]: [
        {
          key: 'box_color',
          name: 'Box Color',
          type: 'Color',
          field: 'boxColor',
          comments: '盒子的背景颜色【在style值为3-D与Filled Frame时出现】'
        }
      ],
      [DspBoxStyle['FILLED-FRAME']]: [
        {
          key: 'line_width',
          name: 'Line Width',
          type: 'Slider',
          field: 'lineWidth',
          range: [0, 10],
          comments: '线条的厚度。即边框的宽度。'
        },
        {
          key: 'box_color',
          name: 'Box Color',
          type: 'Color',
          field: 'boxColor',
          comments: '盒子的背景颜色'
        },
        {
          key: 'line_color',
          name: 'Line Color',
          type: 'Color',
          field: 'lineColor',
          comments: '线条的颜色'
        }
      ]
    }
  }
]
export const dspBoxDefault: Record<string, any> = {
  ...baseDefault,
  width: 200,
  height: 60,
  style: DspBoxStyle.FRAME,
  line_width: 3,
  line_color: 'LightGray',
  box_color: 'LightGray',
  box_radius: 5
}
