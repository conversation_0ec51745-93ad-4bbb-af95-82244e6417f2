<template>
  <div>
    <h2>Data Formats</h2>
    <wui-form
      ref="formatFormRef"
      label-width="230"
      label-position="left"
      validate-box-style="fill"
      validate-msg-position="right"
      validate-ellipsis="2"
      hide-required-asterisk
      status-icon
      :model="dataFormatList"
      :rules="formatRules"
    >
      <wui-form-item label="Time Format" prop="time_format">
        <div :class="styles.ext">
          <wui-select
            v-model="dataFormatList.time_format"
            placeholder="Select"
            @change="onDataFormatChange('time_format', $event)"
          >
            <wui-option
              v-for="item in options3"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
        </div>
      </wui-form-item>
      <wui-form-item label="Date Format" prop="date_format">
        <div :class="styles.ext">
          <wui-select
            v-model="dataFormatList.date_format"
            placeholder="Select"
            @change="onDataFormatChange('date_format', $event)"
          >
            <wui-option
              v-for="item in options4"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
        </div>
      </wui-form-item>
      <wui-form-item label="Param Color" prop="param_color">
        <div :class="styles.ext">
          <wui-select
            v-model="dataFormatList.param_color"
            placeholder="Select"
            @change="onDataFormatChange('param_color', $event)"
          >
            <wui-option
              v-for="item in colorData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </wui-select>
        </div>
      </wui-form-item>
    </wui-form>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed } from 'vue'
import styles from '../index.module.scss'
import { useCore } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { DataFormatOptions } from '@wuk/cfg'
import { WuiForm, WuiFormItem } from '@wuk/wui'
import { useFormatRules } from './rule'
const { formatRules, setFormatError } = useFormatRules()
const formatFormRef = ref<InstanceType<typeof WuiForm>>()
const mainPtr = useCore<BizMain>(BizMain)
const dataFormatList = ref<DataFormatOptions>({
  time_format: 1,
  date_format: 1,
  param_color: ''
})

const options3 = [
  {
    label: 'Standard',
    value: 0
  },
  {
    label: 'Military',
    value: 1
  }
]
const options4 = [
  {
    label: 'YYMMDD',
    value: 0
  },
  {
    label: 'YYYYMMDD',
    value: 1
  },
  {
    label: 'MMDDYY',
    value: 2
  },
  {
    label: 'MMDDYYYY',
    value: 3
  },
  {
    label: 'DDMMYY',
    value: 4
  },
  {
    label: 'DDMMYYYY',
    value: 5
  },
  {
    label: 'MM_D_Y',
    value: 6
  }
]

const onDataFormatChange = async (key: string, value: string | number) => {
  setFormatError(key)
  const valid = await formatFormRef.value?.validateField(key)
  if (!valid) return
  const result = await mainPtr.value?.writeDataFormat({
    [key]: value
  })
  if (!result) {
    setFormatError(key, `faild to save`)
    formatFormRef.value?.validateField(key)
    return
  }
}

const colorData = computed(
  () =>
    mainPtr.value?.colors?.list().map(item => {
      const label = mainPtr.value?.colors?.forKey(item)
      return {
        label,
        value: label
      }
    }) || []
)

onMounted(async () => {
  dataFormatList.value = (await mainPtr.value?.readDataFormat()) || ({} as DataFormatOptions)
})
</script>
