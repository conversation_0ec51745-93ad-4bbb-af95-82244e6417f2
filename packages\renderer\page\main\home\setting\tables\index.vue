<template>
  <div>
    <h2>System Tables</h2>
    <div :class="styles.pb_100">
      <wui-table
        border
        :data="tableData6"
        :class="styles.grid"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
      >
        <wui-table-column prop="name" label="Group Name" align="center" show-overflow-tooltip />
        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default>
            <wui-icon @click="openModel(1, 'tableIcon')">
              <EditPen />
            </wui-icon>
          </template>
        </wui-table-column>
      </wui-table>
    </div>

    <MyDialog
      v-model="bModelShow"
      title="Table Group: viscosity"
      width="600px"
      @ok="openModel(1, 'submit')"
    >
      <div class="tabsBox">
        <wui-button>Operators</wui-button>
        <wui-button style="margin-left: 6px">Functions</wui-button>
        <wui-button style="margin-left: 6px">Parameters</wui-button>
      </div>
      <div :class="styles.tableBg">
        <wui-table
          :data="tableData8"
          border
          height="200px"
          style="width: 100%"
          :header-cell-style="{
            background: '#EAF1FD',
            color: '#90AFE4',
            fontSize: '18px',
            fontWeight: 'bold'
          }"
        >
          <wui-table-column prop="name" label="Name" align="center" show-overflow-tooltip />
          <wui-table-column prop="equation" label="Equation" align="center" show-overflow-tooltip />
          <wui-table-column label="Op" fixed="right" width="60px" align="center">
            <template #default>
              <wui-icon @click="openModel(2, 'tableIcon')">
                <EditPen />
              </wui-icon>
            </template>
          </wui-table-column>
        </wui-table>
      </div>
    </MyDialog>

    <MyDialog
      v-model="cModelShow"
      title="Table Group: fuelflow Table:KFactor2_T"
      width="760px"
      @ok="openModel(2, 'submit')"
    >
      <div :class="styles.form">
        <div :class="styles.form_box">
          <div :class="styles.form_box_content">
            <span>Expression: </span>
            <span>{{ 'TRUE' }}</span>
          </div>
          <div style="margin-left: 30px">
            <span>Review Date: </span>
            <span>{{ '17 Nov 2021' }}</span>
          </div>
        </div>
        <div :class="styles.form_box">
          <div :class="styles.form_box_content">
            <span>Manual Date: </span>
            <span>{{ '18 Dec 2024' }}</span>
          </div>
          <div
            :class="styles.form_box_content"
            style="margin-left: 30px; width: calc(100% - 220px)"
          >
            <span style="width: 200px">Manual Revision: </span>
            <wui-input
              v-model="input"
              placeholder="Please input"
              style="width: 100%; height: 25px; margin-left: 0"
            />
          </div>
        </div>
        <div style="margin-bottom: 10px">
          <div>Comments/Reference:</div>
          <wui-input
            v-model="input"
            placeholder="Please input"
            type="textarea"
            style="width: 100%; margin-left: 0"
            resize="none"
            :rows="2"
          />
        </div>
        <div :class="styles.form_box" style="justify-content: space-between">
          <div :class="[styles.form_box_content, styles.form_box_change]">
            <span>Table Type: </span>
            <wui-select v-model="selecValue7" placeholder="Select" style="width: 130px">
              <wui-option
                v-for="item in options14"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
          </div>
          <div :class="[styles.form_box_content, styles.form_box_change]">
            <span>Store To Database: </span>
            <wui-switch
              v-model="switchShow10"
              style="--wui-switch-on-color: #53c41a; --wui-switch-off-color: #d4d3d1"
            />
          </div>
          <div :class="[styles.form_box_content, styles.form_box_change]">
            <span>Dynamic Expression: </span>
            <wui-switch
              v-model="switchShow11"
              style="--wui-switch-on-color: #53c41a; --wui-switch-off-color: #d4d3d1"
            />
          </div>
        </div>
      </div>
      <div :class="styles.tableBg">
        <wui-table
          v-if="btnShow1"
          :data="tableData9"
          height="200px"
          border
          style="width: 100%"
          :header-cell-style="{
            background: '#EAF1FD',
            color: '#90AFE4',
            fontSize: '18px',
            fontWeight: 'bold'
          }"
        >
          <wui-table-column prop="output" label="Output" align="center" show-overflow-tooltip />
          <wui-table-column prop="xInput" label="X Input" align="center" show-overflow-tooltip />
          <wui-table-column prop="yInput" label="X Input" align="center" show-overflow-tooltip />>
        </wui-table>
        <wui-input
          v-if="btnShow2"
          v-model="input"
          placeholder="Please input"
          type="textarea"
          style="width: 100%; margin-left: 0"
          resize="none"
          :rows="9"
        />
        <div v-if="btnShow3" style="height: 200px; border: 1px solid #bbbbbb"></div>
      </div>
      <div :class="styles.modelBox">
        <div
          :class="styles.modelBox_btn"
          :style="{ background: btnShow1 ? '#ffffff' : '#cecece' }"
          @click="onChangeBtn(1)"
        >
          Column List
        </div>
        <div
          :class="styles.modelBox_btn"
          :style="{ background: btnShow2 ? '#ffffff' : '#cecece' }"
          @click="onChangeBtn(2)"
        >
          Text
        </div>
        <div
          :class="styles.modelBox_btn"
          :style="{ background: btnShow3 ? '#ffffff' : '#cecece' }"
          @click="onChangeBtn(3)"
        >
          View Table
        </div>
      </div>
    </MyDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import styles from '../index.module.scss'
import { EditPen } from '@element-plus/icons-vue'

const tableData6: any = [
  // {
  //   name: 'viscossity'
  // },
  // {
  //   name: 'viscossity'
  // },
  // {
  //   name: 'viscossity'
  // }
]
const tableData8 = [
  {
    name: 'Visc_T',
    equation: '(FuelType-"AV-Gas")'
  },
  {
    name: 'Visc_T',
    equation: '(FuelType-"AV-Gas")'
  }
]
const tableData9 = [
  {
    output: 3.0,
    xInput: -10.0,
    yInput: -10.0
  },
  {
    output: 4.0,
    xInput: 0.0,
    yInput: 0.0
  },
  {
    output: 5.0,
    xInput: 25.0,
    yInput: 25.0
  },
  {
    output: 6.0,
    xInput: 32.0,
    yInput: 32.0
  },
  {
    output: 7.0,
    xInput: 38.0,
    yInput: 38.0
  }
]
const options14 = [
  {
    label: 'Polynomial',
    value: '1'
  },
  {
    label: 'Constant',
    value: '2'
  },
  {
    label: '2D',
    value: '3'
  },
  {
    label: '3D',
    value: '4'
  }
]
const selecValue7 = ref('')
const input = ref('')
const bModelShow = ref(false)
const cModelShow = ref(false)
const switchShow10 = ref(true)
const switchShow11 = ref(true)
const btnShow1 = ref(true)
const btnShow2 = ref(false)
const btnShow3 = ref(false)

const openModel = (i: number, type: string) => {
  if (type === 'tableIcon') {
    switch (i) {
      case 1:
        bModelShow.value = true
        cModelShow.value = false
        break
      case 2:
        bModelShow.value = false
        cModelShow.value = true
        break
      default:
        break
    }
  } else {
    switch (i) {
      case 1:
        bModelShow.value = false
        break
      case 2:
        cModelShow.value = false
        break
      default:
        break
    }
  }
  btnShow1.value = true
  btnShow2.value = false
  btnShow3.value = false
}

const onChangeBtn = (i: number) => {
  if (i === 1) {
    btnShow1.value = true
    btnShow2.value = false
    btnShow3.value = false
  } else if (i === 2) {
    btnShow1.value = false
    btnShow2.value = true
    btnShow3.value = false
  } else {
    btnShow1.value = false
    btnShow2.value = false
    btnShow3.value = true
  }
}
</script>

<style lang="scss" scoped>
.tabsBox {
  :deep(.wui-button) {
    width: auto;
    margin-bottom: 10px;
    color: #181010;
    --wui-button-hover-border-color: #d3d3d3;
    --wui-button-hover-bg-color: rgba(250, 250, 250, 0.1);
    --wui-button-hover-text-color: #181010;
    --wui-button-active-border-color: #dcdfe6;
  }
}
</style>
