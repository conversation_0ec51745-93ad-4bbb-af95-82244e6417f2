<template>
  <div :class="styles.welcome">
    <div :class="styles.welcome_version">
      <span :class="styles.welcome_version_title">Version</span>
      <div :class="styles.welcome_version_content">
        <span :class="styles.welcome_start">Current version: {{ versionList.name || '' }}</span>
        <span :class="styles.welcome_start">Comments: {{ versionList.comments || '' }}</span>
      </div>
    </div>

    <div :class="styles.welcome_engines">
      <span :class="styles.welcome_engines_title">Engines</span>
      <div :class="styles.welcome_engines_content">
        <div
          :class="styles.welcome_engines_item"
          v-for="(item, index) in list"
          :key="`${index}-${item.name}`"
          @mouseenter="item.isShow = true"
          @mouseleave="item.isShow = false"
          @click="handleEngineClick(item)"
        >
          <img v-if="item.isShow" src="./image/opt.png" alt="" />
          <img v-else src="./image/normalcy.png" alt="" />
          <div :class="styles.welcome_engines_item_text">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="Welcome">
import styles from './index.module.scss'
import { useBizInvoke, useBizMain, useHandler } from '@/renderer/hooks'
import { onMounted, ref } from 'vue'
import { BizMain, AppEngine } from '@/renderer/logic'
import { CfgVersion } from '@wuk/cfg'

interface newAppEngine extends AppEngine {
  label: string
  name: string
  isShow: boolean
}

const list = ref<newAppEngine[]>()
const mainPtr = useBizMain()
const invokePtr = useBizInvoke()
const versionList = ref<CfgVersion>({
  name: '',
  md5: '',
  comments: ''
})

const handleEngineClick = async (item: newAppEngine) => {
  if (await mainPtr.value?.openEngine(item.name)) {
    invokePtr.value?.showEngine()
  }
}

const getDataInfo = async () => {
  const data = (await mainPtr.value?.getEngines()) || []
  list.value = data.map(item => {
    return { ...item, isShow: false }
  })
}

useHandler(mainPtr, BizMain.onEnginesChanged, getDataInfo)

useHandler(mainPtr, BizMain.onVersionsChanged, () => {
  versionList.value = mainPtr.value?.version || ({} as CfgVersion)
})

onMounted(async () => {
  await getDataInfo()
  versionList.value = mainPtr.value?.version || ({} as CfgVersion)
})
</script>
