import { defineComponent, inject, nextTick, onMounted, Ref, ref, useModel, watchEffect } from 'vue'
import CodeEditor, { CodeEditorExpose } from '@/renderer/components/CodeEditor'
import { useBizEngine } from '@/renderer/hooks'
import { calcContextKey } from '../constants'
import { EditRowCodeExpose } from './type'

export default defineComponent({
  name: 'EditRowCode',
  props: {
    permanentFocus: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:permanentFocus'],
  setup(props, { expose }) {
    const calcContext = inject(calcContextKey)
    if (!calcContext) return
    const codeEditorRef = ref()
    const codeTxt = ref('')
    let fileStart = ''
    let fileEnd = ''
    const bizEngine = useBizEngine()
    const permanentFocus = useModel(props, 'permanentFocus')
    const handleSave = async () => {
      const { curEditCalcInfo, changeTreeNode } = calcContext
      const { groupNodeIndex = -1, children = [] } = curEditCalcInfo
      const originData = children[groupNodeIndex].originData
      const res = await bizEngine.value?.saveCalcText(
        originData.file,
        `${fileStart}\n${codeTxt.value}\n${fileEnd}`,
        originData.type
      )
      if (!res) return
      curEditCalcInfo?.calcId && changeTreeNode?.(curEditCalcInfo.calcId)
    }

    const loadCode = async () => {
      const { groupNodeIndex = -1, children = [] } = calcContext.curEditCalcInfo
      if (groupNodeIndex === -1) return
      const originData = children[groupNodeIndex].originData
      const res = (await bizEngine.value?.loadCalcText(originData.file, originData.type)) || ''
      const arr = res.split('\n')
      fileStart = arr.splice(0, 1)[0]
      fileEnd = arr.splice(arr.length - 1, 1)[0]
      codeTxt.value = arr.join('\n')
    }

    watchEffect(loadCode, {
      flush: 'post'
    })

    expose<EditRowCodeExpose>({
      handleSave,
      loadCode,
      code: codeEditorRef as any
    })
    return () => (
      <>
        <CodeEditor
          ref={codeEditorRef}
          v-model:permanentFocus={permanentFocus.value}
          v-model:code={codeTxt.value}
          height='100%'
        />
      </>
    )
  }
})
