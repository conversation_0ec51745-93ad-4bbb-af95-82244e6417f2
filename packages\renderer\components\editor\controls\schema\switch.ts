import { DspSwitchType } from '@wuk/wui'
import { baseConfig, BaseConfigType, baseDefault } from '../common'
import {
  ColorConfigType,
  ParamConfigType,
  SelectConfigType,
  SliderConfigType,
  SwitchConfigType
} from '../types'

export type DspSwitchConfigType =
  | BaseConfigType
  | ColorConfigType
  | SelectConfigType<string, DspSwitchConfigType>
  | SliderConfigType
  | ParamConfigType
  | SwitchConfigType<number>

// param_id: string
// type: string
// on_label: string
// off_label: string
// off_color: string
// on_color: string
// hide_box_shadow: number
// radius: string
// font_size: number
// font_weight: number
// has_release: boolean
// release_msg: CalMsg
// release_vec: Array<string>
// has_push: boolean
// push_msg: CalMsg
// push_vec: Array<string>

// CalMsg {
//   uuid: string
//   line_num: number
//   path: string
// }
export const dspSwitchConfig: Array<DspSwitchConfigType> = [
  ...baseConfig,
  {
    key: 'param_id',
    name: 'Param Id',
    type: 'Param',
    required: true
  },
  {
    key: 'type',
    name: 'Switch Type',
    type: 'Select',
    comments: '开关类型',
    range: [
      { key: DspSwitchType.MOMENTARY, text: 'Momentary' },
      { key: DspSwitchType.TOGGLE, text: 'Toggle' },
      { key: DspSwitchType.STATUS, text: 'Status' },
      { key: DspSwitchType.ACTION, text: 'Action' }
    ]
  },
  {
    key: 'on_label',
    name: 'On Label',
    type: 'Text',
    field: 'onLabel',
    comments: '激活时显示在开关上的文本'
  },
  {
    key: 'off_label',
    name: 'Off Label',
    type: 'Text',
    field: 'offLabel',
    comments: '关闭时显示在开关上的文本'
  },
  {
    key: 'off_color',
    name: 'Off Color',
    type: 'Color',
    field: 'offColor',
    comments: '关闭开关的背景颜色'
  },
  {
    key: 'on_color',
    name: 'On Color',
    type: 'Color',
    field: 'onColor',
    comments: '打开开关的背景颜色'
  },
  {
    key: 'hide_box_shadow',
    name: 'Hide Box Shadow',
    type: 'Switch',
    field: 'hideBoxShadow',
    range: [0, 1],
    comments: '隐藏阴影'
  },
  {
    key: 'radius',
    name: 'Switch Radius',
    type: 'Slider',
    field: 'switchRadius',
    comments: '圆角半径',
    range: [0, 50]
  },
  {
    key: 'font_size',
    name: 'Font Size',
    field: 'labelFontSize',
    comments: 'switch按钮内部文本字体大小',
    type: 'Slider',
    range: [5, 100]
  },
  {
    key: 'font_weight',
    name: 'Font Weight',
    type: 'Slider',
    field: 'labelFontWeight',
    comments: 'switch按钮内部文本字体粗细',
    range: [100, 900],
    step: 100
  }
  // {
  //   key: 'label_color',
  //   name: 'Label Color',
  //   type: 'Color',
  //   field: 'labelColor',
  //   comments: 'switch按钮内部文本字体颜色',
  //   default: '#000000'
  // },
  // {
  //   key: 'shadow_color',
  //   name: 'Shadow Color',
  //   type: 'Color',
  //   field: 'shadowColor',
  //   comments: 'switch按钮 outer 阴影颜色',
  //   default: 'rgba(0, 0, 0, 0.14)'
  // },
  // {
  //   key: 'label_hover_color',
  //   name: 'Label Hover Color',
  //   type: 'Color',
  //   field: 'labelHoverColor',
  //   comments: 'switch按钮 label hover 字体颜色',
  //   default: '#000000'
  // }
]

export const dspSwitchDefault: Record<string, any> = {
  ...baseDefault,
  param_id: 'None',
  width: 200,
  height: 60,
  type: DspSwitchType.MOMENTARY,
  on_label: 'On',
  off_label: 'Off',
  off_color: 'LightGray',
  on_color: 'Green',
  radius: 5,
  font_size: 23,
  font_weight: 400,
  // labelColor: '#000000',
  // shadowColor: 'rgba(0, 0, 0, 0.14)',
  // labelHoverColor: '#000000',
  hide_box_shadow: 0
}
