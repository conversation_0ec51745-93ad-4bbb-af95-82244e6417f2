<template>
  <div>
    <h2>Audio Options</h2>
    <wui-form
      ref="audioFormRef"
      label-width="0"
      label-position="left"
      validate-box-style="fill"
      validate-ellipsis="1"
      hide-required-asterisk
      inline-message
      status-icon
      validate-box-gap="3"
      validate-placement="bottom"
      :model="audioModel"
      :rules="audioRules"
    >
      <wui-form-item
        label="Audio Host Address"
        label-width="230"
        prop="hostAddress"
        :rules="audioRules.hostAddress"
      >
        <wui-input
          v-model="audioModel.hostAddress"
          placeholder="Please input"
          @change="onAudioChange('hostAddress')"
        />
      </wui-form-item>
      <div>
        <h3>Sound List</h3>
        <wui-table
          border
          :data="audioModel.audioList"
          :class="styles.grid"
          :header-cell-style="{
            background: '#EAF1FD',
            color: '#90AFE4',
            fontSize: '18px',
            fontWeight: 'bold'
          }"
          @row-contextmenu="onRightClick"
        >
          <wui-table-column
            label="Sound Name"
            min-width="160px"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <wui-form-item
                v-if="row.flag"
                :prop="`audioList.${$index}.name`"
                :rules="audioRules.name"
              >
                <wui-input
                  v-model="row.name"
                  clearable
                  placeholder="Please input"
                  style="width: 100%; margin-left: 0; height: 32px"
                />
              </wui-form-item>
              <span v-else>{{ row.name }}</span>
            </template>
          </wui-table-column>
          <wui-table-column
            label="Sound File"
            min-width="160px"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <wui-form-item
                v-if="row.flag"
                :prop="`audioList.${$index}.file`"
                :rules="audioRules.file"
              >
                <wui-input
                  v-model="row.file"
                  clearable
                  placeholder="Please input"
                  style="width: 100%; margin-left: 0; height: 32px"
                />
              </wui-form-item>
              <span v-else>{{ row.file }}</span>
            </template>
          </wui-table-column>
          <wui-table-column label="Priority" min-width="160px" align="center" show-overflow-tooltip>
            <template #default="{ row }">
              <wui-select
                v-if="row.flag"
                v-model="row.priority"
                placeholder="Select"
                style="width: 100%; margin-left: 0"
              >
                <wui-option
                  v-for="item in options1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </wui-select>
              <span v-else>{{ convertType(options1, row.priority) }}</span>
            </template>
          </wui-table-column>
          <wui-table-column label="Repeat" width="160px" align="center" show-overflow-tooltip>
            <template #default="{ row, $index }">
              <wui-form-item
                v-if="row.flag"
                :prop="`audioList.${$index}.repeat`"
                :rules="audioRules.repeat"
              >
                <wui-input
                  type="number"
                  v-model.number="row.repeat"
                  clearable
                  placeholder="Please input"
                  style="width: 100%; margin-left: 0; height: 32px"
                />
              </wui-form-item>
              <span v-else>{{ row.repeat }}</span>
            </template>
          </wui-table-column>
          <wui-table-column label="Op" fixed="right" width="100px" align="center">
            <template #default="{ row, $index }">
              <wui-icon v-if="!row.flag" @click="onEdit(row)">
                <EditPen />
              </wui-icon>
              <div v-else :class="styles.box_options_btn">
                <wui-icon @click="onConfirm(row, $index)">
                  <Select />
                </wui-icon>
                <wui-popconfirm
                  title="Are you sure you want to cancel this?"
                  @confirm="onClose(row, $index)"
                  width="295px"
                >
                  <template #reference>
                    <wui-icon>
                      <CloseBold />
                    </wui-icon>
                  </template>
                </wui-popconfirm>
              </div>
            </template>
          </wui-table-column>
          <template #empty>
            <div @contextmenu.prevent="addTableColumn">
              <p>No Data</p>
            </div>
          </template>
        </wui-table>
      </div>
    </wui-form>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import styles from '../index.module.scss'
import { useRightMenu, useHandler, useBizMain } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { AudioOptions, AudoItem } from '@wuk/cfg'
import { WuiMessage, WuiForm, WuiFormItem } from '@wuk/wui'
import { convertType } from '@/renderer/utils/common'
import { EditPen, Select, CloseBold } from '@element-plus/icons-vue'
import { useAudioRules } from './rule'
interface newAudoItem extends AudoItem {
  meta?: AudoItem
  flag: boolean
  type: string
}
const audioFormRef = ref<InstanceType<typeof WuiForm>>()
const { audioRules, setAudioError } = useAudioRules()
const mainPtr = useBizMain()
const createRow = (): newAudoItem => ({
  name: '',
  file: '',
  repeat: 0,
  priority: 0,
  flag: true,
  type: 'addType'
})
const audioModel = reactive({
  hostAddress: '',
  audioList: [] as newAudoItem[]
})
const options1 = [
  {
    label: 'Urgent',
    value: 0
  },
  {
    label: 'High',
    value: 1
  },
  {
    label: 'Normal',
    value: 2
  },
  {
    label: 'Low',
    value: 3
  }
]

const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90
  })
}
const onAudioChange = async (key: string) => {
  setAudioError(key)
  const valid = await audioFormRef.value?.validateField(key)
  if (!valid) return
  const result = await mainPtr.value?.setHostAddress(audioModel.hostAddress)
  if (!result) {
    setAudioError(key, `Audio host address fail to save`)
    return
  }
}

// 新增事件
const onAdd = () => {
  audioModel.audioList.push(createRow())
}
// 编辑事件
const onEdit = (item: newAudoItem) => {
  if (item.flag) return
  item.flag = true
}
// 当表格没有数据，右键点击表格的事件
const menuAdd = useRightMenu([{ key: 'addKey', label: 'add' }], () => {
  onAdd()
})
const addTableColumn = (event: MouseEvent) => {
  event.preventDefault()
  menuAdd.show(event.clientX, event.clientY)
}
// 右键菜单
const menuPtr = useRightMenu(
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'midifyKey', label: 'midify' },
    { key: 'deleteKey', label: 'delete' }
  ],
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        onAdd()
        break
      case 'insertKey':
        audioModel.audioList.splice(rowIndex + 1, 0, { ...createRow(), type: 'insertType' })
        break
      case 'midifyKey':
        onEdit(row)
        break
      case 'deleteKey':
        {
          const removeResult = await mainPtr.value?.removeAudio(rowIndex)
          if (!removeResult) return
          tipsMessage()
        }
        break
      default:
        break
    }
  }
)
// 右键点击表格事件
const onRightClick = (row: any, column: any, event: MouseEvent) => {
  event.preventDefault()
  let rowIndex = -1
  rowIndex = audioModel.audioList.indexOf(row)
  menuPtr.show(event.clientX, event.clientY, { row, rowIndex })
}
// 关闭事件
const onClose = (item: newAudoItem, index: number) => {
  const { type, meta = {} } = item
  const { name, file, repeat, priority } = meta as AudoItem
  if (type === 'addType' || type === 'insertType') {
    audioModel.audioList.splice(index, 1)
  } else {
    item.name = name
    item.file = file
    item.repeat = repeat
    item.priority = priority
    item.flag = false
  }
}
// 提交事件
const onConfirm = async (item: newAudoItem, index: number) => {
  const { name, file, repeat, priority, type } = item
  const valids = await audioFormRef.value?.validateField([
    `audioList.${index}.name`,
    `audioList.${index}.file`,
    `audioList.${index}.repeat`
  ])
  if (!valids) return
  const data = { name, file, repeat, priority }
  let editResult
  if (type === 'addType' || type === 'insertType') {
    editResult = await mainPtr.value?.addAudio(data, type === 'insertType' ? index : undefined)
  } else {
    editResult = await mainPtr.value?.modifyAudio(index, data)
  }
  if (!editResult) return
  item.flag = false
  tipsMessage()
}
// 数据处理
const getDataInfo = async () => {
  const { audio_host_address, list = [] } =
    (await mainPtr.value?.readAudioOptions()) || ({} as AudioOptions)
  audioModel.hostAddress = audio_host_address
  audioModel.audioList = list.map(item => {
    const meta = item
    const flag = false
    const type = ''
    return { ...item, meta, flag, type }
  })
}

useHandler(mainPtr, BizMain.onAudioOptionChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
})
</script>
