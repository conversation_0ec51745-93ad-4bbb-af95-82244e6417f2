import { ActionKey, AppMenuItem } from '@wuk/cfg'
import { useCore } from './boots'
import { onBeforeUnmount, Ref, ref, toRaw, watch } from 'vue'
import { BizInvoke } from '../logic'

let MenuActionKey = 1
export const useRightMenu = (
  items: AppMenuItem[],
  handler?: (key: ActionKey, ...args: any[]) => void
) => {
  const handlerRef = ref<any>()
  handlerRef.value = handler
  const objRef = ref<string>(`boots::use::menu::${++MenuActionKey}`)
  const argsRef = ref<any[]>([])
  let menuList: AppMenuItem[] = toRaw(items)

  const handleMenuClicked = (actionKey: ActionKey, key: ActionKey) => {
    if (objRef.value !== actionKey) return

    handlerRef.value?.(key, ...argsRef.value)
  }

  const invoke = useCore<BizInvoke>(BizInvoke, itr =>
    itr.on(BizInvoke.onMenuClicked, handleMenuClicked)
  )

  const init = (menus: AppMenuItem[]) => {
    menuList = toRaw(menus)
  }

  const show = (x: number, y: number, ...args: any[]) => {
    argsRef.value = args
    invoke.value?.showRightMenu(objRef.value, menuList, x, y)
  }

  onBeforeUnmount(() => invoke.value?.off(BizInvoke.onMenuClicked, handleMenuClicked))

  return {
    init,
    show
  }
}

/**
 * @description 表格通用右键菜单
 */
export const useTableCommonMenu = <T, P = { row: T; rowIndex: number }>(
  list: Ref<T[]>,
  handler?: (key: ActionKey, ...args: [P, ...any[]]) => void,
  range: [number, number?] = [1],
  items: AppMenuItem[] = [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' }
  ]
) => {
  const { show, init } = useRightMenu([], handler)
  /**
   * 初始menu
   */
  watch(
    () => list.value.length,
    len => {
      const menus = [...items]
      const min = range[0]
      const max = range[1] ?? menus.length - min
      !len && menus.splice(min, max)
      init(menus)
    },
    {
      immediate: true
    }
  )
  const handleRowMenu = (row: T, _: any, event: MouseEvent) => {
    const rowIndex = list.value.indexOf(row)
    show(event.clientX, event.clientY, { row, rowIndex })
  }
  const handleTableAreaCtxMenu = (event: MouseEvent) => {
    event.preventDefault()
    show(event.clientX, event.clientY, {})
  }
  return {
    show,
    init,
    handleRowMenu,
    handleTableAreaCtxMenu
  }
}
