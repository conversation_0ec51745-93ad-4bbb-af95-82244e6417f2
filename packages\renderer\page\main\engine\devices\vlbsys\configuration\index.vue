<template>
  <TreeContent
    ref="treeContentRef"
    v-model:default-expanded-keys="defaultExpandedKeys"
    :onTree-node-change="handleNodeChange"
    :tree-data="treeData"
    :tree-area-menu="[]"
    :show-right-menu="false"
    :right-content-padding="0"
  >
    <template #default>
      <VibrationConfigTable v-if="isGroupMode" @db-click="dbClick" />
      <VibrationConfigSet v-else />
    </template>
  </TreeContent>
</template>

<script lang="ts" setup>
import { ref, onMounted, toRaw, computed, reactive, provide } from 'vue'
import { useBizEngine, useHandler, useTableCommonMenu } from '@/renderer/hooks'
import { SetupOptions } from '@wuk/cfg'
import { Tree, TreeContent } from '@/renderer/components/TreeContent'
import VibrationConfigTable from './vibrationConfigTable/index.vue'
import VibrationConfigSet from './vibrationConfigSet/index.vue'
import { uuid } from '@wuk/cfg'
import { contextKey } from './consts'
import { BizEngine } from '@/renderer/logic'

const treeContentRef = ref<InstanceType<typeof TreeContent>>()
const curEditVibInfo = ref({
  label: '',
  vibId: -1,
  index: 0,
  groupNodeIndex: -1,
  children: []
})

const mode = ref<'setup-group' | 'setup-config'>('setup-group')
const devicePtr = useBizEngine()

const handleNodeChange = (data: any, node: any) => {
  const { level, parent } = node
  if (level === 1) {
    mode.value = 'setup-group'
    curEditVibInfo.value = {
      ...data,
      vibId: data.id,
      groupNodeIndex: -1
    }
  } else if (level === 2) {
    const { data: parentData } = parent
    mode.value = 'setup-config'
    curEditVibInfo.value = {
      ...parentData,
      vibId: parentData.id,
      groupNodeIndex: data.index ?? -1
    }
  }
}

const treeData = ref<any[]>([
  {
    label: 'VibConfig',
    id: 0,
    index: 0,
    children: []
  }
])

const changeTreeNode = async (id: number) => {
  treeContentRef.value?.treeRef?.setCurrentKey(id)
}

const defaultExpandedKeys = ref<number[]>([0])

const isGroupMode = computed(() => mode.value === 'setup-group')

provide(
  contextKey,
  reactive({
    devicePtr,
    curEditVibInfo,
    changeTreeNode
  })
)

const getDataInfo = async () => {
  const list = (await devicePtr.value?.readSetups()) || ([] as SetupOptions[])
  treeData.value[0].children = list.map((item, index) => ({
    label: item.name,
    id: uuid(),
    index,
    originData: item
  }))
}

const dbClick = (data: any) => {
  mode.value = 'setup-config'
  curEditVibInfo.value = {
    ...data,
    vibId: data.id,
    groupNodeIndex: data.index ?? -1
  }
  changeTreeNode(data.id)
}

useHandler(devicePtr, BizEngine.onVibrationSetupChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
  treeContentRef.value?.treeRef?.setCurrentKey(0)
})
</script>
