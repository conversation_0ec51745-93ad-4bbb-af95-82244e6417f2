import { CodeEditorExpose } from '@/renderer/components/CodeEditor'
import type { BaseTableDefaultScope, BaseTableRow } from '@/renderer/components/TableTool'
import type { CalcLineable, CalcWhenToExcuteType } from '@wuk/cfg'
import { EditMode } from '../../CalcTool'
export type EquationTableRow = BaseTableRow<
  CalcLineable & {
    units: string // 缺失
  }
>
export type EquationTableRowKey = keyof EquationTableRow
export type EquationTableRowScope = BaseTableDefaultScope<EquationTableRow>
export type EquationHeaderProps = {
  label?: string
}
export type ModelType = {
  when_to_excute: CalcWhenToExcuteType
  editMode: EditMode
  list: EquationTableRow[]
}

export type EditRowTableExpose = {
  handleSave: () => Promise<void>
}
export type EditRowCodeExpose = {
  handleSave: () => Promise<void>
  loadCode: () => Promise<void>
  code: CodeEditorExpose
}
