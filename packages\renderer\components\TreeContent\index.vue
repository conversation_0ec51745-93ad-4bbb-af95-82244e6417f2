<template>
  <div :class="b()">
    <panel-group direction="horizontal">
      <panel :default-size="20" :min-size="20">
        <div :class="e('left')" @contextmenu="treeAreaCtxMenu">
          <WuiTree
            ref="treeRef"
            :props="{ class: () => e('left', 'tree', 'item') }"
            :class="e('left', 'tree')"
            :data="treeData"
            :draggable
            :indent="5"
            :default-expanded-keys="defaultExpandedKeys"
            highlight-current
            node-key="id"
            :allow-drag
            :allow-drop
            @node-drop="(...args: any) => $emit('node-drop', ...args)"
            @node-contextmenu="handleNodeMenu"
            @current-change="handleNodeChange"
            @node-expand="handleNodeToggle($event, true)"
            @node-collapse="handleNodeToggle($event, false)"
          />
        </div>
      </panel>
      <panel-resize-handle :class="e('resize', 'handle')" />
      <panel :min-size="50">
        <div :class="e('right')">
          <div v-if="!!$slots.header" :class="e('right', 'header')">
            <slot name="header" />
          </div>
          <div
            v-if="showRightBorder"
            :class="{
              [e('right', 'content')]: true,
              [m('header', 'right', 'content')]: !!$slots.header
            }"
            :style="{
              padding: `${rightContentPadding}px`
            }"
          >
            <slot />
          </div>
          <template v-else>
            <slot />
          </template>
        </div>
      </panel>
    </panel-group>
  </div>
</template>

<script setup lang="tsx">
import { ref, watch, defineModel, onMounted } from 'vue'
import { Panel, PanelGroup, PanelResizeHandle } from 'vue-resizable-panels'
import { useBem } from '@/renderer/hooks/bem'
import { WuiTree } from '@wuk/wui'
import styles from './index.module.scss'
import { useRightMenu } from '@/renderer/hooks'
import type { TreeProps } from './type'
import { Tree } from './type'

const props = withDefaults(defineProps<TreeProps>(), {
  treeData: () => [],
  treeAreaMenu: () => [],
  showRightBorder: true,
  showRightMenu: true,
  rightContentPadding: 20,
  draggable: false
})
const emits = defineEmits([
  'tree-node-contextmenu',
  'tree-node-change',
  'tree-area-contextmenu',
  'tree-menu-init',
  'node-drop'
])

const { b, e, m } = useBem('treecontent', styles)
const defaultExpandedKeys = defineModel<(number | string)[]>('defaultExpandedKeys', {
  default: []
})
const treeRef = ref<InstanceType<typeof WuiTree>>()
// 右键点击表格事件以及菜单
const menuPtr = useRightMenu([], async (key, ...args) => {
  emits('tree-area-contextmenu', key, ...args)
})
let showCtxMenu = () => {}

const handleNodeMenu = (...args: any[]) => {
  if (!props.showRightMenu) return
  const [event, ...params] = args
  emits('tree-menu-init', 'Node')
  showCtxMenu = () => {
    menuPtr.show(event.clientX, event.clientY, ...params)
  }
  emits('tree-node-contextmenu', ...args)
}
const treeAreaCtxMenu = (event: MouseEvent, ...args: any[]) => {
  if (!props.showRightMenu) return
  event.preventDefault()
  emits('tree-menu-init', 'Area')
  showCtxMenu = () => {
    menuPtr.show(event.clientX, event.clientY, ...args)
  }
}

const handleNodeToggle = (data: Tree, isExpand: boolean) => {
  if (isExpand) {
    defaultExpandedKeys.value = [...defaultExpandedKeys.value, data.id]
    return
  }
  defaultExpandedKeys.value = defaultExpandedKeys.value.filter(key => key !== data.id)
}

const handleNodeChange = (...args: any) => {
  emits('tree-node-change', ...args)
}
const initRightMenu = () => {
  if (!props.showRightMenu) return
  watch(
    () => props.treeAreaMenu,
    menu => {
      menuPtr.init(menu)
      showCtxMenu()
      showCtxMenu = () => {}
    }
  )
}
onMounted(initRightMenu)
defineExpose({
  treeRef
})
</script>
