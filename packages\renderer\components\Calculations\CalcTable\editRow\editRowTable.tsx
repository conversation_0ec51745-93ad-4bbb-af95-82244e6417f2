import { defineComponent, PropType } from 'vue'
import { FormModel, TableType } from '../editRow'
import { WuiInput, WuiButton, WuiTable, WuiTableColumn } from '@wuk/wui'
import { useBem, useBizEngine } from '@/renderer/hooks'
import $styles from './index.module.scss'
import { useParameterDialog } from '@/renderer/utils/common'
export default defineComponent({
  name: 'EditRowTable',
  props: {
    formModel: {
      type: Object as PropType<FormModel>,
      required: true
    }
  },
  setup(props) {
    const { b, e, m } = useBem('editrow', $styles)
    const bizEngine = useBizEngine()
    const handleSelectParamter = (formModel: any, key: string) => {
      return async () => {
        const res = await useParameterDialog()
        formModel[key] = res
      }
    }
    return () => {
      const { formModel } = props
      const isThreeD = formModel.tableType === TableType['3D']
      const xProp = isThreeD ? 'coord3' : 'coord2'
      return (
        <WuiTable
          height='100%'
          maxHeight='185px'
          data={formModel.corrData}
          border
          class={e('table')}>
          <WuiTableColumn prop='coord1' align='center'>
            {{
              default: ({ row }: any) => <WuiInput v-model={row.coord1} class={e('table-input')} />,
              header: () => <span>Output: {formModel.output || 'None'}</span>
            }}
          </WuiTableColumn>
          <WuiTableColumn prop={xProp} align='center'>
            {{
              default: ({ row }: any) => <WuiInput v-model={row[xProp]} class={e('table-input')} />,
              header: () => (
                <div>
                  X Input:
                  <WuiButton onClick={handleSelectParamter(formModel, 'xInput')}>
                    {formModel.xInput || 'None'}
                  </WuiButton>
                </div>
              )
            }}
          </WuiTableColumn>
          {isThreeD && (
            <WuiTableColumn prop='coord2' align='center'>
              {{
                default: ({ row }: any) => (
                  <WuiInput v-model={row.coord2} class={e('table-input')} />
                ),
                header: () => (
                  <div>
                    Y Input:
                    <WuiButton onClick={handleSelectParamter(formModel, 'yInput')}>
                      {formModel.yInput || 'None'}
                    </WuiButton>
                  </div>
                )
              }}
            </WuiTableColumn>
          )}
        </WuiTable>
      )
    }
  }
})
