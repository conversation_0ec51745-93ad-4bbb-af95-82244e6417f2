import { defineComponent, reactive, toRef, useModel } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { useBem, useBizEngine, useTableCommonMenu } from '@/renderer/hooks'
import $styles from './index.module.scss'
import TableTool, { isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import { SetupCounterRow, SetupCounterDeafultScope } from '../type'
import ColorSelect from '@/renderer/components/ColorSelect/index.tsx'

export default defineComponent({
  name: 'SetupLimitCounters',
  props: {
    showLimitCounter: {
      type: Boolean,
      default: false
    },
    limitCounters: {
      type: Array as () => SetupCounterRow[],
      default: () => []
    }
  },
  emits: ['update:showLimitCounter'],
  setup(props) {
    const showLimitCounter = useModel(props, 'showLimitCounter')
    const limitCounters = useModel(props, 'limitCounters')
    const originList = JSON.parse(JSON.stringify(props.limitCounters))
    const bizEngine = useBizEngine()
    const { b, e, m } = useBem('setup-limit-counters', $styles)
    const model = reactive({
      list: limitCounters.value
    })
    const createRow = (row_type: SetupCounterRow['row_type']) => ({
      color: '',
      param_id: '',
      flag: true,
      row_type
    })
    const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
      toRef(model, 'list'),
      (key, ...args) => {
        const { row, rowIndex } = args[0]
        switch (key) {
          case 'addKey':
            model.list.push(createRow('add'))
            break
          case 'deleteKey':
            handleOp('delete', row, rowIndex)
            break
          case 'modifyKey':
            handleOp('edit', row, rowIndex)
            break
          case 'insertKey':
            model.list.splice(rowIndex + 1, 0, createRow('insert'))
            break
        }
      }
    )
    const handleOp = (op: OpType, row: SetupCounterRow, index: number) => {
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handleDelete(index)
          break
        case 'select':
          handleSelect(row, index)
          row.row_type = '*'
          row.flag = false
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
      }
    }
    const handleOpCancel = (row: SetupCounterRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.list.splice(index, 1)
        return
      }
      row.flag = false
      row.color = originList[index].color
      row.param_id = originList[index].param_id
    }
    const handleSelect = async (row: SetupCounterRow, index: number) => {
      let res: boolean | undefined
      const { row_type, flag, ...params } = row
      if (row.row_type === 'add') {
        res = await bizEngine.value?.addLimitCounter(params, index)
      } else {
        res = await bizEngine.value?.modifyLimitCounter(index, params)
      }
      if (!res) return
    }
    const handleDelete = async (index: number) => {
      const res = await bizEngine.value?.removeLimitCounter(index)
      if (!res) return
      model.list.splice(index, 1)
    }
    const handleOk = async () => {
      showLimitCounter.value = false
    }
    const columns: Array<{
      prop: keyof SetupCounterRow
      width?: string
      align?: string
    }> = [
      { prop: 'color', width: '180px', align: 'center' },
      { prop: 'param_id', align: 'center' }
    ]
    return () => {
      return (
        <>
          <MyDialog
            width='600px'
            v-model={showLimitCounter.value}
            title='Limit Counters'
            onOk={handleOk}>
            <div class={[e('body'), 'cfg-setup']}>
              <div class={['cfg-setup_table', e('body', 'table')]}>
                <wui-table
                  onRow-contextmenu={handleRowMenu}
                  show-header={false}
                  border
                  height='100%'
                  data={model.list}>
                  {{
                    default: () => (
                      <>
                        {columns.map(column => (
                          <>
                            <wui-table-column {...column}>
                              {{
                                default: ({ row }: SetupCounterDeafultScope) =>
                                  row.flag ? (
                                    column.prop === 'color' ? (
                                      <ColorSelect v-model={row[column.prop]} />
                                    ) : (
                                      <wui-input
                                        v-model={row[column.prop]}
                                        placeholder='Please Input'
                                        clearable
                                      />
                                    )
                                  ) : (
                                    row[column.prop]
                                  )
                              }}
                            </wui-table-column>
                          </>
                        ))}
                        <wui-table-column width='100px' align='center'>
                          {{
                            default: ({ row, $index }: any) => (
                              <TableTool.Op
                                flag={row.flag}
                                onOp={op => handleOp(op, row, $index)}
                              />
                            )
                          }}
                        </wui-table-column>
                      </>
                    ),
                    empty: () => <TableTool.Empty onCustom-contextmenu={handleTableAreaCtxMenu} />
                  }}
                </wui-table>
              </div>
            </div>
          </MyDialog>
        </>
      )
    }
  }
})
