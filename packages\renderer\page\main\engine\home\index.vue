<template>
  <div :class="styles.box">
    <div :class="styles.box_search">
      <div :class="styles.box_search_title">Home</div>
      <div :class="styles.box_search_searchImg">
        <wui-input v-model="SearchInput" placeholder="Search Setting" clearable />
        <div :class="styles.box_search_searchImg_image">
          <img src="/packages/renderer/assets/search.png" alt="" />
        </div>
      </div>
    </div>
    <div :class="styles.box_menu">
      <div :class="styles.box_menu_anchor">
        <wui-anchor :container="containerRef" ref="anchorRef" :offset="150" @click="handleClick">
          <wui-anchor-link href="#engine" title="Engine Options" />
          <wui-anchor-link href="#valid" title="Valid Engine Dash Numbers" />
        </wui-anchor>
      </div>
      <div ref="containerRef" :class="styles.box_menu_content">
        <EngineList :class="styles.box_menu_options" id="engine" />
        <wui-divider border-style="dashed" />
        <EngineTable :class="styles.box_menu_optionsTable" id="valid" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import styles from './index.module.scss'
import { ref, onMounted, nextTick } from 'vue'
import EngineList from './engineList/index.vue'
import EngineTable from './engineTable/index.vue'

const SearchInput = ref('')
const containerRef = ref<HTMLElement | null>(null)
const handleClick = (e: MouseEvent) => {
  e.preventDefault()
}
const anchorRef = ref()
onMounted(() => {
  nextTick(() => {
    window.scrollTo(0, 0)
    anchorRef.value.scrollTo('#engine')
  })
})
</script>

<style lang="scss" stylemodule>
.wui-divider--horizontal {
  width: 98%;
  margin: 20px 0;
}
.wui-select {
  width: 230px;
  height: 32px;
}
.wui-select__wrapper {
  &:hover {
    box-shadow: 0 0 0 1px #b0cdff !important;
  }
}
.wui-input {
  width: 230px;
  height: 32px;
}
.wui-input__inner {
  height: 40px;
}
.wui-anchor {
  margin-top: 40px;
}
.wui-anchor__item {
  height: 50px;
}
.wui-anchor__link {
  font-size: 14px !important;
  font-weight: normal;
  line-height: 41px;
  padding-left: 20px;
  color: #3d3d3d;
}
.wui-anchor__link:focus {
  color: #12151a;
}
.wui-anchor__link:hover {
  color: #12151a;
  background-color: #e6efff;
}
.wui-anchor__link.is-active {
  background-color: #b0cdff;
  color: #12151a;
  font-size: 18px !important;
  font-weight: 500;
  height: 100%;
  line-height: 41px;
  padding-left: 20px;
}
.wui-anchor__marker {
  display: none;
}
.wui-anchor.wui-anchor--vertical .wui-anchor__list {
  padding-left: 0;
}
.wui-input__icon {
  font-size: 16px;
  color: #444444;
}
.wui-icon {
  cursor: pointer;
  &:hover {
    color: #0c65ff;
  }
}
.wui-popconfirm__action {
  display: flex;
  justify-content: center;
  .wui-button:nth-child(1) {
    display: block !important;
    background-color: #f0f0f0 !important;
    border: 1px solid #b6bece !important;
    color: #3d3d3d !important;
    &:hover {
      background-color: #909399 !important;
      color: #ffffff !important;
    }
  }
  .wui-button:nth-child(2) {
    background-color: #6282c1 !important;
    border: 1px solid #3c5992 !important;
    &:hover {
      background-color: rgba(98, 130, 193, 0.7) !important;
    }
  }
}
.wui-table {
  color: #3d3d3d;
  font-size: 18px;
  font-weight: bold;
  --wui-table-row-hover-bg-color: #b0cdff;
}
.wui-table__empty-text {
  width: 100%;
}
.wui-dialog__title {
  font-size: 14px !important;
  font-weight: bold;
}
</style>
