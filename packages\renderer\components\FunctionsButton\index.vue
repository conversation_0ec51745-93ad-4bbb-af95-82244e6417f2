<template>
  <wui-popover
    v-model="state.visible"
    trigger="click"
    :width="350"
    :show-arrow="false"
    placement="bottom"
  >
    <wui-scrollbar :wrap-class="e('wrap')" :view-class="e('view')" @click="handleClickItem">
      <div v-for="(func, index) in funcs" :key="index" :data-name="$name" :class="e('item')">
        {{ func }}
      </div>
    </wui-scrollbar>
    <template #reference>
      <wui-button @click="handleClickBtn">{{ name }}</wui-button>
    </template>
  </wui-popover>
</template>
<script setup lang="ts">
import { useBem } from '@/renderer/hooks'
import { WuiPopover, WuiScrollbar } from '@wuk/wui'
import $styles from './index.module.scss'
import { reactive, ref } from 'vue'
interface Props {
  name?: string
}
const emits = defineEmits(['select'])
// func：标识
const $name = 'func-item'
const { e } = useBem('funcs-btn', $styles)
withDefaults(defineProps<Props>(), {
  name: 'Functions'
})
/**
 * @description: 状态
 * @param {boolean} visible 是否显示
 */
const state = reactive({
  visible: false
})
const funcs = ref([
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp',
  'sin',
  'cos',
  'tan',
  'log',
  'ln',
  'exp'
])

/**
 * @description: 点击事件(委托)
 */
const handleClickItem = (e: MouseEvent) => {
  const target = e.target as HTMLElement
  if (target.dataset.name !== $name) return
  emits('select', target.innerText)
  state.visible = false
}
const handleClickBtn = () => {
  state.visible = !state.visible
}
</script>
