import { baseConfig, BaseConfigType, baseDefault } from '../common'
import {
  ColorConfigType,
  ParamConfigType,
  SelectConfigType,
  SliderConfigType,
  SwitchConfigType
} from '../types'
import { DspButtonType } from '@wuk/wui'

export type DspButtonConfigType =
  | BaseConfigType
  | ColorConfigType
  | SelectConfigType<number | string, DspButtonConfigType>
  | SliderConfigType
  | ParamConfigType
  | SwitchConfigType<string>

// type: string
// param_id: string
// set_value: number
// on_label: string
// off_label: string
// label_color: string
// on_color: string
// off_color: string
// radius: string
// font_size: number
// font_weight: number
export const dspButtonConfig: Array<DspButtonConfigType> = [
  ...baseConfig,
  {
    key: 'type',
    name: 'Button Type',
    type: 'Select',
    field: 'type',
    comments: '按钮类型',
    range: [
      { key: DspButtonType.RADIO, text: 'Radio' },
      { key: DspButtonType.PUSH, text: 'Push' },
      { key: DspButtonType.TOGGLE, text: 'Toggle' },
      { key: DspButtonType.ACTIVATEPHASE, text: 'Activate Phase' }
    ]
  },
  {
    key: 'param_id',
    name: 'Parameter',
    type: 'Param',
    comments: '绑定测试参数值'
  },
  {
    key: 'set_value',
    name: 'Set Value',
    type: 'Number',
    comments:
      '指定按钮参数的设定值。要输入到Radio按钮或者Set按钮打开状态的参数中的值。对于Activate Phase按钮和Toggle按钮而言，该参数的值设置为1。0表示关闭'
  },
  {
    key: 'on_label',
    name: 'On Label',
    type: 'Text',
    field: 'onLabel',
    comments: '激活按钮显示的值'
  },
  {
    key: 'off_label',
    name: 'Off Label',
    field: 'offLabel',
    type: 'Text',
    comments: '关闭按钮显示的值'
  },
  {
    key: 'label_color',
    name: 'Label Color',
    field: 'labelColor',
    type: 'Color',
    comments: '按钮内部文本字体颜色'
  },
  {
    key: 'on_color',
    name: 'On Color',
    field: 'onColor',
    type: 'Color',
    comments: '激活时按钮的背景色'
  },
  {
    key: 'off_color',
    name: 'Off Color',
    field: 'offColor',
    type: 'Color',
    comments: '关闭按钮显示的背景色'
  },
  {
    key: 'radius',
    name: 'Button Radius',
    field: 'buttonRadius',
    type: 'Slider',
    comments: '圆角半径',
    range: [0, 50]
  },
  {
    key: 'font_size',
    name: 'Label Font Size',
    field: 'labelFontSize',
    comments: '按钮内部文本字体大小',
    type: 'Slider',
    range: [5, 100]
  },
  {
    key: 'font_weight',
    name: 'Label Font Weight',
    field: 'labelFontWeight',
    type: 'Slider',
    comments: '按钮内部文本字体粗细',
    step: 100,
    range: [100, 900]
  }
]
export const dspButtonDefault: Record<string, any> = {
  ...baseDefault,
  width: 200,
  height: 60,
  type: DspButtonType.TOGGLE,
  param_id: 'None',
  set_value: -1,
  on_label: 'On',
  off_label: 'Off',
  label_color: 'Black',
  on_color: 'Green',
  off_color: 'LightGray',
  radius: 5,
  font_size: 23,
  font_weight: 400
}
