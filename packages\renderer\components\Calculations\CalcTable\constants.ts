import { InjectionKey } from 'vue'
import { Tree } from '../../TreeContent'
import { TableData, TableLib } from '@wuk/cfg'

// todo:::临时
type CalcTableOption = {
  name: string
  equation: string
}
export interface CalcTableTreeChild extends Omit<Tree<number>, 'children'> {
  originData: {
    group_name: string
    tables: Array<TableData>
  }
}
export interface CalcTableTree extends Tree<number> {
  children?: CalcTableTreeChild[]
}

export type CurTableGroupInfo = {
  groupName: string
  label: string
  calcTableId: number
  index?: number
  groupNodeIndex: number
  children?: CalcTableTreeChild[]
}

export type CurTableEqInfo = {
  tableIndex: number
  item: Partial<TableData>
  tableLib?: TableLib
}

interface CalcTableContext {
  curTableGroupInfo: CurTableGroupInfo
  curTableEqInfo: CurTableEqInfo
  changeTreeNode: (id: number) => void
}
export const calcTableContextKey: InjectionKey<CalcTableContext> = Symbol('calcTableContextKey')
