<template>
  <div>
    <h2>Print Modes</h2>
    <div>
      <wui-checkbox
        v-model="printList.print_messages"
        label="Print Messages"
        @change="onPrintChange('print_messages', $event)"
      />
      <wui-checkbox
        v-model="printList.print_stored_scans"
        label="Print Stored Scans"
        @change="onPrintChange('print_stored_scans', $event)"
      />
      <wui-checkbox
        v-model="printList.print_stored_comments"
        label="Print Stored Comment"
        @change="onPrintChange('print_stored_comments', $event)"
      />
      <wui-checkbox
        v-model="printList.print_stored_displays"
        label="Print Stored Displays"
        @change="onPrintChange('print_stored_displays', $event)"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useCore } from '@/renderer/hooks'
import { BizMain } from '@/renderer/logic'
import { PrintModeOptions } from '@wuk/cfg'
import { WuiMessage } from '@wuk/wui'

const mainPtr = useCore<BizMain>(BizMain)
const printList = ref<PrintModeOptions>({
  print_messages: false,
  print_stored_scans: false,
  print_stored_comments: false,
  print_stored_displays: false
})

const onPrintChange = async (key: string, value: string | number) => {
  const result = await mainPtr.value?.writePrintModes({
    [key]: value
  })
  if (!result) return
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90
  })
}

onMounted(async () => {
  printList.value = (await mainPtr.value?.readPrintModes()) || ({} as PrintModeOptions)
})
</script>
