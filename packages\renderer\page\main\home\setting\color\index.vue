<template>
  <div>
    <h2>Color Options</h2>
    <wui-form
      ref="colorFormRef"
      label-width="0"
      label-position="left"
      validate-box-style="fill"
      validate-ellipsis="1"
      hide-required-asterisk
      inline-message
      status-icon
      validate-box-gap="3"
      validate-placement="bottom"
      :model="colorModel"
      :rules="colorRules"
    >
      <wui-table
        border
        :data="colorModel.colorList"
        :class="styles.grid"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
        @row-contextmenu="handleRightClick"
      >
        <wui-table-column label="Name" min-width="120px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item
              v-if="row.flag"
              :prop="`colorList.${$index}.name`"
              :rules="colorRules.name"
            >
              <wui-input
                v-model="row.name"
                clearable
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
              />
            </wui-form-item>
            <span v-else>{{ row.name }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Red" min-width="120px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item v-if="row.flag" :prop="`colorList.${$index}.r`" :rules="colorRules.r">
              <wui-input-number
                :model-value="row.r"
                :min="0"
                :max="255"
                clearable
                :controls="false"
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
                @input="onInput($event, 'r', row, $index)"
              />
            </wui-form-item>
            <span v-else>{{ row.r }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Green" min-width="120px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item v-if="row.flag" :prop="`colorList.${$index}.g`" :rules="colorRules.g">
              <wui-input-number
                :model-value="row.g"
                :min="0"
                :max="255"
                clearable
                :controls="false"
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
                @input="onInput($event, 'g', row, $index)"
              />
            </wui-form-item>
            <span v-else>{{ row.g }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Blue" min-width="120px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item v-if="row.flag" :prop="`colorList.${$index}.b`" :rules="colorRules.b">
              <wui-input-number
                :model-value="row.b"
                :min="0"
                :max="255"
                clearable
                :controls="false"
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
                @input="onInput($event, 'b', row, $index)"
              />
            </wui-form-item>
            <span v-else>{{ row.b }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Alpha" min-width="120px" align="center" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <wui-form-item v-if="row.flag" :prop="`colorList.${$index}.a`" :rules="colorRules.a">
              <wui-input-number
                :model-value="row.a"
                :min="0"
                :max="255"
                clearable
                :controls="false"
                placeholder="Please input"
                style="width: 100%; margin-left: 0; height: 32px"
                @input="onInput($event, 'a', row, $index)"
              />
            </wui-form-item>
            <span v-else>{{ row.a }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default="{ row, $index }">
            <wui-icon v-if="!row.flag" @click="onEdit(row)">
              <EditPen />
            </wui-icon>
            <div v-else :class="styles.box_options_btn">
              <wui-icon @click="onConfirm(row, $index)">
                <Select />
              </wui-icon>
              <wui-popconfirm
                title="Are you sure you want to cancel this?"
                @confirm="onClose(row, $index)"
                width="295px"
              >
                <template #reference>
                  <wui-icon>
                    <CloseBold />
                  </wui-icon>
                </template>
              </wui-popconfirm>
            </div>
          </template>
        </wui-table-column>
        <template #empty>
          <div @contextmenu.prevent="addTableColumn">
            <p>No Data</p>
          </div>
        </template>
      </wui-table>
    </wui-form>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import styles from '../index.module.scss'
import { useCore, useRightMenu, useHandler, useBizMain } from '@/renderer/hooks'
import { WuiMessage, WuiForm, WuiFormItem } from '@wuk/wui'
import { BizMain } from '@/renderer/logic'
import { ColorOptions, RgbItem } from '@wuk/cfg'
import { EditPen, Select, CloseBold } from '@element-plus/icons-vue'
import { useColorRules } from './rule'
interface newRgbItem extends RgbItem {
  meta?: RgbItem
  flag: boolean
  type: string
}
const colorFormRef = ref<InstanceType<typeof WuiForm>>()
const { colorRules, setColorError } = useColorRules()
const mainPtr = useBizMain()
const createRow = (): newRgbItem => ({
  name: '',
  r: 0,
  g: 0,
  b: 0,
  a: 255,
  flag: true,
  type: 'addType'
})

const colorModel = reactive({
  colorList: [] as newRgbItem[]
})
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90
  })
}
const onInput = async (val: any, key: string, row: Record<string, any>, index: number) => {
  row[key] = val || null
  try {
    const valid = await colorFormRef.value?.validateField(`colorList.${index}.${key}`)
    console.log(`colorList.${index}.${key}`, valid, val)
  } catch (error) {
    console.warn(error)
  }
}
// 新增事件
const onAdd = () => {
  colorModel.colorList.push(createRow())
}
// 编辑事件
const onEdit = (item: newRgbItem) => {
  if (item.flag) return
  item.flag = true
}
// 当表格没有数据，右键点击表格的事件
const menuAdd = useRightMenu([{ key: 'addKey', label: 'add' }], () => {
  onAdd()
})
const addTableColumn = (event: MouseEvent) => {
  event.preventDefault()
  menuAdd.show(event.clientX, event.clientY)
}
// 右键菜单
const menuPtr = useRightMenu(
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'midifyKey', label: 'midify' },
    { key: 'deleteKey', label: 'delete' }
  ],
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        onAdd()
        break
      case 'insertKey':
        colorModel.colorList.splice(rowIndex + 1, 0, { ...createRow(), type: 'insertType' })
        break
      case 'midifyKey':
        onEdit(row)
        break
      case 'deleteKey':
        {
          const removeResult = await mainPtr.value?.removeColor(rowIndex)
          if (!removeResult) return
          tipsMessage()
        }
        break
      default:
        break
    }
  }
)
const handleRightClick = (row: any, column: any, event: MouseEvent, title?: string) => {
  event.preventDefault()
  let rowIndex = -1
  rowIndex = colorModel.colorList.indexOf(row)
  menuPtr.show(event.clientX, event.clientY, { row, rowIndex, title })
}
// 关闭事件
const onClose = (item: newRgbItem, index: number) => {
  const { type, meta = {} } = item
  const { name, r, g, b, a } = meta as RgbItem
  if (type === 'addType' || type === 'insertType') {
    colorModel.colorList.splice(index, 1)
  } else {
    item.name = name
    item.r = r
    item.g = g
    item.b = b
    item.a = a
    item.flag = false
  }
}
// 提交事件
const onConfirm = async (item: newRgbItem, index: number) => {
  const { name, r, g, b, a = 255, type } = item
  const keys = ['name', 'r', 'g', 'b', 'a'].map(key => `colorList.${index}.${key}`)
  const valids = await colorFormRef.value?.validateField(keys)
  if (!valids) return
  const data = { name, r, g, b, a }
  let editResult
  if (type === 'addType' || type === 'insertType') {
    editResult = await mainPtr.value?.addColor(data, type === 'insertType' ? index : undefined)
  } else {
    editResult = await mainPtr.value?.modifyColor(index, data)
  }
  if (!editResult) return
  item.flag = false
  tipsMessage()
}
// 数据处理
const getDataInfo = async () => {
  const { list = [] } = (await mainPtr.value?.readColorOptions()) || ({} as ColorOptions)
  colorModel.colorList = list.map(item => {
    const meta = item
    const flag = false
    const type = ''
    return { ...item, meta, flag, type }
  })
}

useHandler(mainPtr, BizMain.onColorOptionChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
})
</script>

<style lang="scss" stylemodule>
.wui-input-number .wui-input__inner {
  text-align: left !important;
}
</style>
