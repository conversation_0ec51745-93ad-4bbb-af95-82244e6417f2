<template>
  <MyDialog v-model="isActive" title="PLC Digital Signal Editor" width="800px" @ok="onSubmit">
    <div :class="styles.modelBox">
      <wui-table
        border
        :data="tableData"
        style="width: 100%"
        height="350px"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
        @row-contextmenu="handleRowMenu"
      >
        <wui-table-column label="Parameter" min-width="140px" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <wui-input
              v-if="row.flag"
              v-model="row.name"
              clearable
              placeholder="Enter parameter name"
              style="width: 100%; height: 32px"
            />
            <span v-else>{{ row.name }}</span>
          </template>
        </wui-table-column>
        <wui-table-column
          label="Signal range"
          min-width="180px"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>
              {{ `${row.signal_range.min} - ${row.signal_range.max}` }}
            </span>
          </template>
        </wui-table-column>

        <wui-table-column label="Unit range" min-width="180px" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <span>
              {{ `${row.units_range.min} - ${row.units_range.max}` }}
            </span>
          </template>
        </wui-table-column>

        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default="{ row, $index }">
            <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
          </template>
        </wui-table-column>

        <template #empty>
          <TableTool.Empty @custom-contextmenu="handleTableAreaCtxMenu" />
        </template>
      </wui-table>
    </div>
  </MyDialog>
</template>

<script lang="ts" setup>
import { onMounted, PropType, ref, inject } from 'vue'
import styles from '../index.module.scss'
import { useVModel } from '@vueuse/core'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { WuiMessage } from '@wuk/wui'
import { useTableCommonMenu } from '@/renderer/hooks'
import TableTool, { OpType, RowType } from '@/renderer/components/TableTool'
import { PlcSignalTableRow, SignalParam, PLCContext, plcContextKey } from '../type'

const props = defineProps({
  params: {
    type: Object as PropType<SignalParam>,
    default: () => ({})
  },
  modelShow: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelShow'])
const isActive = useVModel(props, 'modelShow', emit)

const plcContext = inject(plcContextKey)
const tableData = ref<PlcSignalTableRow[]>([])

// 使用标准的表格菜单钩子
const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
  tableData,
  async (key, ...args) => {
    const { row, rowIndex } = args[0] || {}
    switch (key) {
      case 'modifyKey':
        if (row) handleOp('edit', row, rowIndex)
        break
      default:
        break
    }
  },
  [1],
  [{ key: 'modifyKey', label: 'modify' }]
)

const handleOp = async (op: OpType, row: PlcSignalTableRow, index: number) => {
  switch (op) {
    case 'edit':
      if (row.flag) return
      row.meta = { ...row }
      row.flag = true
      break
    case 'select':
      await onConfirm(row, index)
      break
    case 'cancel':
      onCancel(row, index)
      break
  }
}

const onConfirm = async (row: PlcSignalTableRow, index: number) => {
  if (!row.name.trim()) {
    WuiMessage({
      message: 'Parameter name cannot be empty',
      type: 'warning',
      offset: 50
    })
    return
  }

  tableData.value[index].name = row.name
  row.flag = false
  row.row_type = '*'
  delete (row as Partial<typeof row>).meta
}

const onCancel = (row: PlcSignalTableRow, index: number) => {
  if (row.meta) {
    Object.assign(row, row.meta)
    row.flag = false
    delete (row as Partial<typeof row>).meta
  }
}

const onSubmit = async () => {
  if (plcContext?.submitSignalData) {
    const signalData = {
      list: tableData.value.map(item => {
        const { flag, row_type, meta, ...signalItem } = item
        return signalItem
      }),
      input_float: true
    }

    await plcContext.submitSignalData({
      currentPlcIndex: props.params.currentPlcIndex,
      plcDeviceIndex: props.params.plcDeviceIndex,
      signalData
    })
  }

  isActive.value = false
}

const getDataInfo = async () => {
  tableData.value =
    plcContext?.plcList.value[props.params.currentPlcIndex]?.devices?.[
      props.params.plcDeviceIndex
    ].signals.list.map(item => {
      const meta = item
      const flag = false
      const row_type: RowType = '*'
      return { ...item, meta, flag, row_type }
    }) || []
}

onMounted(async () => {
  await getDataInfo()
})
</script>
