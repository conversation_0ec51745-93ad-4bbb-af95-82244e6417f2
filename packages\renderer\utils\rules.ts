import { FormItemRule } from '@wuk/wui'
import { Ref } from 'vue'
import message from '../page/main/home/<USER>'
/**
 * @description 校验是否为stringnumber/number类型 - 整数
 */
export const validStrNumIntOrNumInt: FormItemRule['validator'] = (
  rule,
  value,
  callback,
  source,
  options
) => {
  value = Number(value)
  if (value && !Number.isInteger(value)) {
    callback(new Error('Please enter a positive integer'))
  } else {
    callback()
  }
}

/**
 * @description 范围
 */
export const validRange = (min: number, max = Infinity): FormItemRule['validator'] => {
  return (rule, value, callback, source, options) => {
    if (!value && value != 0) callback()
    if (value < min || value > max) {
      if (max === Infinity) {
        callback(new Error(`Please enter a number greater than or equal to ${min}`))
      } else {
        callback(new Error(`Please enter a number between ${min} and ${max}`))
      }
    } else {
      callback()
    }
  }
}

/**
 * @description 验证是否为正数
 */
export const validNum: FormItemRule['validator'] = (rule, value, callback, source, options) => {
  if (!value && value != 0) callback()
  if (!/^(\-|\+)?\d+(\.\d+)?$/.test(value)) {
    callback(new Error('Please enter a number'))
  } else {
    callback()
  }
}

/**
 * @description 接口校验
 */
export const validApi = (errors: Record<string, string>): FormItemRule['validator'] => {
  return (rule, value, callback, source, options) => {
    const msg = errors[rule.field || '']
    if (msg) {
      callback(new Error(msg))
    } else {
      callback()
    }
  }
}

export const setRuleErrorCallback = (
  errors: Ref<Record<string, string>>,
  key: string,
  value = ''
) => {
  if (!value) {
    delete errors.value[key]
    return
  }
  errors.value[key] = value
}

/**
 * @description
 * 1、校验每行维度是否正确
 * 2、校验每行数据点是否为数字
 * @param str 代码
 * @param dim 维度
 * @returns
 */
export function validDataCode(str: string, dim: number) {
  const lines = str.trim().split('\n')
  const numberRegex = /^-?(\d+(\.\d*)?|\.\d+)$/
  const data: number[] = []
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue // 跳过空行

    const dataItems = line.split(/\s+/).filter(item => item !== '')

    // 1. 检查每个数据项是否为有效数字
    for (const item of dataItems) {
      if (!numberRegex.test(item)) {
        return {
          valid: false,
          message: `Incorrect number of values entered on row ${i + 1}`
        }
      }
      data.push(Number(item))
    }

    // 2. 检查数据项数量是否超过维度
    if (dataItems.length > dim) {
      return {
        valid: false,
        message: `Incorrect dimension on row ${i + 1}, the correct dimension is ${dim}`
      }
    }
  }
  return {
    valid: true,
    message: 'success',
    data
  }
}
