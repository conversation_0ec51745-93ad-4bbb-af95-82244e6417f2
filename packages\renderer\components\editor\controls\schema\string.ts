import { DspStringParamBox } from '@wuk/wui'
import { baseConfig, BaseConfigType, baseDefault } from '../common'
import {
  ColorConfigType,
  OperatorType,
  ParamConfigType,
  SelectConfigType,
  SliderConfigType,
  TableConfigType
} from '../types'

export type DspStringConfigType =
  | BaseConfigType
  | ColorConfigType
  | SliderConfigType
  | ParamConfigType
  | SelectConfigType<number | string>

// param_id: string
// label_space: number
// label: string
// label_color: string
// string_color: string
// param_box: number
// param_box_color: string
// shading: number
// item_vec: Array<StringItem>
// string_font_weight: number // todo：reader待实现
// string_font_size: number // todo：reader待实现
// string_radius: string // todo：reader待实现
// label_font_weight: number // todo：reader待实现
// label_font_size: number // todo：reader待实现
// StringItem {
//   color: string
//   op: string
//   value: string
//   text: string
// }
const dspStringItemConfig: Array<DspStringConfigType> = [
  {
    key: 'color',
    name: 'Color',
    type: 'Color',
    comments: '颜色值'
  },
  {
    key: 'op',
    name: 'Op',
    type: 'Select',
    range: [
      { key: OperatorType.Greater, text: OperatorType.Greater },
      { key: OperatorType.Less, text: OperatorType.Less },
      { key: OperatorType.Equal, text: OperatorType.Equal }
    ]
  },
  {
    key: 'value',
    name: 'Test Value',
    type: 'Param',
    comments: '比对值|parameter'
  },
  {
    key: 'text',
    name: 'Text',
    type: 'Text',
    comments: 'String控件右侧值'
  }
]
export const dspStringConfig: Array<DspStringConfigType | TableConfigType<DspStringConfigType>> = [
  ...baseConfig,
  {
    key: 'string_radius',
    name: 'String Radius',
    type: 'Slider',
    field: 'stringRadius',
    range: [0, 10],
    comments: 'String box的圆角弧度'
  },
  {
    key: 'string_font_weight',
    name: 'String Font Weight',
    type: 'Slider',
    field: 'stringFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'string字体的粗细程度'
  },
  {
    key: 'string_font_size',
    name: 'String Font Size',
    type: 'Slider',
    range: [5, 100],
    field: 'stringFontSize',
    comments: 'string字体大小'
  },
  {
    key: 'label_font_weight',
    name: 'Label Font Weight',
    type: 'Slider',
    field: 'labelFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'label字体的粗细程度'
  },
  {
    key: 'label_font_size',
    name: 'Label Font Size',
    type: 'Slider',
    range: [5, 100],
    field: 'labelFontSize',
    comments: 'label字体大小'
  },
  {
    key: 'param_id',
    name: 'Param Id',
    type: 'Param',
    required: true
  },
  {
    key: 'label_space',
    name: 'Label Space',
    type: 'Slider',
    field: 'labelSpace',
    range: [0, 33],
    comments: '控件左侧label最左侧到右侧value最左侧之间的间距 单位：【空格 em】'
  },
  {
    key: 'label',
    name: 'Label',
    type: 'Text',
    comments: '控件左侧label值'
  },
  {
    key: 'label_color',
    name: 'Label Color',
    type: 'Color',
    field: 'labelColor',
    comments: '左侧label的字体颜色'
  },
  {
    key: 'param_box',
    name: 'Param Box',
    type: 'Select',
    field: 'paramBox',
    range: [
      { key: DspStringParamBox.NONE, text: 'None' },
      { key: DspStringParamBox.OUTLINE, text: 'Outline' },
      { key: DspStringParamBox.FILLED, text: 'Filled' }
    ],
    comments: 'parambox属性有三个值：0：none 无（透明） 1：outline 边框 2：filled 填充'
  },
  {
    key: 'param_box_color',
    name: 'Param Box Color',
    type: 'Color',
    field: 'paramBoxColor',
    comments: 'parambox为1时右侧value的边框颜色，parambox为2时右侧value的背景/边框颜色'
  },
  {
    key: 'shading',
    name: 'Shading',
    type: 'Slider',
    field: 'shading',
    range: [0, 10],
    comments: '边框的宽度'
  },
  {
    key: 'item_vec',
    name: 'State List',
    type: 'Table',
    column: ['color', 'op', 'value', 'text'],
    row: dspStringItemConfig,
    comments: '',
    field: 'stateList'
  }
  // {
  //   key: 'string_value',
  //   name: 'String Value',
  //   type: 'Text',
  //   field: 'stringValue',
  //   comments: '右侧动态字符串内容',
  // },
  // {
  //   key: 'label_font_size',
  //   name: 'Label Font Size',
  //   type: 'Text',
  //   field: 'labelFontSize',
  //   comments: 'label font size',
  // },
  // {
  //   key: 'label_font_weight',
  //   name: 'Label Font Weight',
  //   type: 'Select',
  //   field: 'labelFontWeight',
  //   range: [
  //     { key: 0, text: 'Normal' },
  //     { key: 1, text: 'Bold' }
  //   ],
  //   comments: 'label font weight',
  // },
  // {
  //   key: 'string_font_size',
  //   name: 'String Font Size',
  //   type: 'Text',
  //   field: 'stringFontSize',
  //   comments: '右侧 value font size',
  // },
  // {
  //   key: 'string_font_weight',
  //   name: 'String Font Weight',
  //   type: 'Select',
  //   field: 'stringFontWeight',
  //   range: [
  //     { key: 0, text: 'Normal' },
  //     { key: 1, text: 'Bold' }
  //   ],
  //   comments: '右侧 value font weight',
  // },
  // {
  //   key: 'string_radius',
  //   name: 'String Radius',
  //   type: 'Text',
  //   field: 'stringRadius',
  //   comments: '字符串圆角',
  // },
  // {
  //   key: 'parameter',
  //   name: 'Parameter',
  //   type: 'Param',
  //   comments: ''
  // },
  // {
  //   key: 'point',
  //   name: 'Point',
  //   type: 'Select',
  //   range: [
  //     { key: 14, text: '14' },
  //     { key: 18, text: '18' },
  //     { key: 20, text: '20' },
  //     { key: 24, text: '24' },
  //     { key: 25, text: '25' },
  //     { key: 34, text: '34' }
  //   ]
  // },
  // {
  //   key: 'weight',
  //   name: 'Weight',
  //   type: 'Select',
  //   range: [
  //     { key: WeightType.Bold, text: 'Bold' },
  //     { key: WeightType.Medium, text: 'Medium' }
  //   ]
  // },
  // {
  //   key: 'slant',
  //   name: 'Slant',
  //   type: 'Select',
  //   range: [
  //     { key: SlantType.R, text: 'R' },
  //     { key: SlantType.O, text: 'O' }
  //   ]
  // },
  // {
  //   key: 'param',
  //   name: '',
  //   type: 'Table',
  //   column: ['Color', 'Op', 'Test Value', 'String'],
  //   row: [
  //     {
  //       key: 'color',
  //       type: 'Color'
  //     },
  //     {
  //       key: 'op',
  //       type: 'Select',
  //       range: [
  //         { key: OperatorType.Greater, text: '>' },
  //         { key: OperatorType.Less, text: '<' },
  //         { key: OperatorType.Equal, text: '=' }
  //       ]
  //     },
  //     {
  //       key: 'test_value',
  //       type: 'Param'
  //     },
  //     {
  //       key: 'string',
  //       type: 'Param'
  //     }
  //   ]
  // }
]

export const dspStringDefault: Record<string, any> = {
  ...baseDefault,
  width: 300,
  height: 60,
  label: 'Default',
  param_id: 'None',
  label_space: 5,
  label_color: 'Black',
  param_box: DspStringParamBox.NONE,
  param_box_color: 'Black',
  shading: 0,
  string_font_weight: 400,
  string_font_size: 23,
  string_radius: 5,
  label_font_weight: 400,
  label_font_size: 23,
  item_vec: [
    {
      color: 'Green',
      op: '=',
      text: 'test',
      value: ''
    }
  ]
  // labelFontSize: 23,
  // labelFontWeight: 0,
  // stringFontSize: 23,
  // stringFontWeight: 0,
  // stringRadius: 5
}
