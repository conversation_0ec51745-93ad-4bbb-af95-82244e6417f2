<template>
  <div :class="[b()]">
    <wui-form
      label-width="160"
      label-position="left"
      validate-box-style="fill"
      validate-msg-position="right"
      validate-ellipsis="2"
      hide-required-asterisk
      status-icon
      label-suffix=":"
      class="cfg-setup"
    >
      <div :class="[e('alarm')]">
        <wui-form-item label="Alarm Input Relay">
          <wui-input
            v-model="model.alarm_relay.input"
            placeholder="Please input"
            @blur="handleBlur('input', model.alarm_relay.input)"
          />
        </wui-form-item>
        <wui-form-item label="Alarm Output Relay">
          <wui-input
            v-model="model.alarm_relay.output"
            placeholder="Please input"
            @blur="handleBlur('output', model.alarm_relay.output)"
          />
        </wui-form-item>
        <wui-form-item label="Alarm Clear Relay">
          <wui-input
            v-model="model.alarm_relay.clear"
            placeholder="Please input"
            @blur="handleBlur('clear', model.alarm_relay.clear)"
          />
        </wui-form-item>
        <wui-button @click="showLimitCounter = true">Counter Editer</wui-button>
      </div>
      <div :class="e('title')">Limit Parameters</div>
      <div :class="['cfg-setup_table', e('table')]">
        <wui-table border height="100%" :data="model.parameters" @row-contextmenu="handleRowMenu">
          <wui-table-column fixed="left" label="No." type="index" width="80px" align="center" />
          <wui-table-column prop="param_id" label="Limit Name" align="center">
            <template #default="{ row }">
              <template v-if="row.flag">
                <wui-input pleaceholder="Please input" v-model="row.param_id" />
              </template>
              <template v-else>
                {{ row.param_id }}
              </template>
            </template>
          </wui-table-column>
          <wui-table-column label="Op" width="100px" align="center">
            <template #default="{ row, $index }">
              <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
            </template>
          </wui-table-column>
          <template #empty>
            <TableTool.Empty @custom-contextmenu="handleTableAreaCtxMenu" />
          </template>
        </wui-table>
      </div>
    </wui-form>
    <setup-limit
      v-model:show-setup-limit="showSetupLimit"
      v-model:current-parameter="currentParameter"
    />
    <setup-limit-counters
      v-model:show-limit-counter="showLimitCounter"
      v-model:limit-counters="model.limit_counters"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, toRef, defineAsyncComponent, ref, watch } from 'vue'
import TableTool, { isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import { useBem, useBizEngine, useHandler, useTableCommonMenu } from '@/renderer/hooks'
import $styles from './index.module.scss'
import { currentParameters, LimitTableRow, SetupLimitOption } from './type'
import { WuiMessage } from '@wuk/wui'
import { cloneFnJSON } from '@vueuse/core'
import { AlarmRelay, LimitOptions } from '@wuk/cfg'
import { BizEngine } from '@/renderer/logic'
import { useParameterDialog } from '@/renderer/utils/common'

const SetupLimit = defineAsyncComponent(() => import('./setupLimit'))
const SetupLimitCounters = defineAsyncComponent(() => import('./setupLimitCounters'))
const { b, e, m } = useBem('limits', $styles)
const bizEngine = useBizEngine()

let originalList = [] as any
const showSetupLimit = ref(false)
const showLimitCounter = ref(false)
const currentParameter = ref<currentParameters>({})
const model = reactive<SetupLimitOption>({
  parameters: [],
  alarm_relay: {} as AlarmRelay,
  limit_counters: []
})

const createRow = (row_type: LimitTableRow['row_type'], param_id: string) => ({
  param_id,
  start: {
    cfg_list: [],
    rate_list: []
  },
  run: {
    cfg_list: [],
    rate_list: []
  },
  row_type,
  flag: true
})

const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
  toRef(model, 'parameters'),
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        const value = await useParameterDialog()
        model.parameters.push(createRow('add', value))
        break
      case 'deleteKey':
        handleOp('delete', row, rowIndex)
        break
      case 'modifyKey':
        handleOp('edit', row, rowIndex)
        break
      case 'insertKey':
        const value1 = await useParameterDialog()
        model.parameters.splice(rowIndex + 1, 0, createRow('insert', value1))
        break
      case 'limitEditerKey':
        showSetupLimit.value = true
        currentParameter.value = { parameter: row, index: rowIndex }
        break
    }
  },
  [1],
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' },
    { key: 'limitEditerKey', label: 'limit editer' }
  ]
)

const readLimits = async () => {
  const limitOption = (await bizEngine.value?.readLimitOptions()) || ({} as LimitOptions)
  console.log(limitOption, 1111)

  model.alarm_relay = limitOption.alarm_relay || {}

  model.parameters = limitOption.parameters.map(item => ({
    param_id: item.param_id,
    start: item.start,
    run: item.run,
    row_type: '*',
    flag: false
  }))

  model.limit_counters = limitOption.limit_counters.map(item => ({
    param_id: item.param_id,
    color: item.color,
    row_type: '*',
    flag: false
  }))

  originalList = limitOption.parameters
}

const handleOp = (op: OpType, row: LimitTableRow, index: number) => {
  switch (op) {
    case 'edit':
      row.flag = true
      break
    case 'delete':
      handleDelete(index)
      break
    case 'select':
      handleSelect(row, index)
      row.row_type = '*'
      row.flag = false
      break
    case 'cancel':
      handleOpCancel(row, index)
      break
  }
}

const handleDelete = async (index: number) => {
  const res = await bizEngine.value?.removeLimitParameter(index)
  if (!res) return
  model.parameters.splice(index, 1)
}

const handleOpCancel = (row: LimitTableRow, index: number) => {
  if (isAddOrInsertType(row.row_type)) {
    model.parameters.splice(index, 1)
    return
  }
  row.param_id = originalList[index].param_id
  row.flag = false
}

const handleSelect = async (row: LimitTableRow, index: number) => {
  let res: boolean | undefined
  const { row_type, flag, ...params } = row
  const data = cloneFnJSON(params)
  if (row.row_type === 'add') {
    res = await bizEngine.value?.addLimitParameter(data, index)
  } else {
    res = await bizEngine.value?.modifyLimitParameter(index, data)
  }
  if (!res) return
}

const handleBlur = async (key: string, value: string) => {
  const result = await bizEngine.value?.modifyAlarmRelay({ [key]: value })
  if (!result) return
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 80
  })
}

useHandler(bizEngine, BizEngine.onLimitOptionsChanged, readLimits)

onMounted(readLimits)
</script>
